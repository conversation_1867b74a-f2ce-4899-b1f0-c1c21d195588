﻿<!DOCTYPE html>
<html lang="en-US" class="no-js">
<head>
    <meta charset="utf-8">
	<title>Single Versus Multi-Families Office Structure - Rockstead</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/","svgExt":".svg","source":{"concatemoji":"static/js/wp-emoji-release.min.js?ver=a101487185b4caba38706df72f6bdc4e"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83d\udc26\u200d\ud83d\udd25","\ud83d\udc26\u200b\ud83d\udd25")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
<meta name='robots' content='noindex, follow'>

<!--[if lte IE 8]>
<link rel='stylesheet' id='wp-embed-template-ie-css' href='static/css/wp-embed-template-ie.min.css' media='all' />
<![endif]-->
<style id='wp-emoji-styles-inline-css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<style id='wp-embed-template-inline-css'>
/*! This file is auto-generated */
body,html{padding:0;margin:0}body{font-family:sans-serif}.screen-reader-text{border:0;clip-path:inset(50%);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;word-wrap:normal!important}.dashicons{display:inline-block;width:20px;height:20px;background-color:transparent;background-repeat:no-repeat;background-size:20px;background-position:center;transition:background .1s ease-in;position:relative;top:5px}.dashicons-no{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M15.55%2013.7l-2.19%202.06-3.42-3.65-3.64%203.43-2.06-2.18%203.64-3.43-3.42-3.64%202.18-2.06%203.43%203.64%203.64-3.42%202.05%202.18-3.64%203.43z%27%20fill%3D%27%23fff%27%2F%3E%3C%2Fsvg%3E")}.dashicons-admin-comments{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M5%202h9q.82%200%201.41.59T16%204v7q0%20.82-.59%201.41T14%2013h-2l-5%205v-5H5q-.82%200-1.41-.59T3%2011V4q0-.82.59-1.41T5%202z%27%20fill%3D%27%2382878c%27%2F%3E%3C%2Fsvg%3E")}.wp-embed-comments a:hover .dashicons-admin-comments{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M5%202h9q.82%200%201.41.59T16%204v7q0%20.82-.59%201.41T14%2013h-2l-5%205v-5H5q-.82%200-1.41-.59T3%2011V4q0-.82.59-1.41T5%202z%27%20fill%3D%27%230073aa%27%2F%3E%3C%2Fsvg%3E")}.dashicons-share{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.5%2012q1.24%200%202.12.88T17.5%2015t-.88%202.12-2.12.88-2.12-.88T11.5%2015q0-.34.09-.69l-4.38-2.3Q6.32%2013%205%2013q-1.24%200-2.12-.88T2%2010t.88-2.12T5%207q1.3%200%202.21.99l4.38-2.3q-.09-.35-.09-.69%200-1.24.88-2.12T14.5%202t2.12.88T17.5%205t-.88%202.12T14.5%208q-1.3%200-2.21-.99l-4.38%202.3Q8%209.66%208%2010t-.09.69l4.38%202.3q.89-.99%202.21-.99z%27%20fill%3D%27%2382878c%27%2F%3E%3C%2Fsvg%3E");display:none}.js .dashicons-share{display:inline-block}.wp-embed-share-dialog-open:hover .dashicons-share{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.5%2012q1.24%200%202.12.88T17.5%2015t-.88%202.12-2.12.88-2.12-.88T11.5%2015q0-.34.09-.69l-4.38-2.3Q6.32%2013%205%2013q-1.24%200-2.12-.88T2%2010t.88-2.12T5%207q1.3%200%202.21.99l4.38-2.3q-.09-.35-.09-.69%200-1.24.88-2.12T14.5%202t2.12.88T17.5%205t-.88%202.12T14.5%208q-1.3%200-2.21-.99l-4.38%202.3Q8%209.66%208%2010t-.09.69l4.38%202.3q.89-.99%202.21-.99z%27%20fill%3D%27%230073aa%27%2F%3E%3C%2Fsvg%3E")}.wp-embed{padding:25px;font-size:14px;font-weight:400;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;line-height:1.5;color:#8c8f94;background:#fff;border:1px solid #dcdcde;box-shadow:0 1px 1px rgba(0,0,0,.05);overflow:auto;zoom:1}.wp-embed a{color:#8c8f94;text-decoration:none}.wp-embed a:hover{text-decoration:underline}.wp-embed-featured-image{margin-bottom:20px}.wp-embed-featured-image img{width:100%;height:auto;border:none}.wp-embed-featured-image.square{float:left;max-width:160px;margin-right:20px}.wp-embed p{margin:0}p.wp-embed-heading{margin:0 0 15px;font-weight:600;font-size:22px;line-height:1.3}.wp-embed-heading a{color:#2c3338}.wp-embed .wp-embed-more{color:#c3c4c7}.wp-embed-footer{display:table;width:100%;margin-top:30px}.wp-embed-site-icon{position:absolute;top:50%;left:0;transform:translateY(-50%);height:25px;width:25px;border:0}.wp-embed-site-title{font-weight:600;line-height:1.78571428}.wp-embed-site-title a{position:relative;display:inline-block;padding-left:35px}.wp-embed-meta,.wp-embed-site-title{display:table-cell}.wp-embed-meta{text-align:right;white-space:nowrap;vertical-align:middle}.wp-embed-comments,.wp-embed-share{display:inline}.wp-embed-meta a:hover{text-decoration:none;color:#2271b1}.wp-embed-comments a{line-height:1.78571428;display:inline-block}.wp-embed-comments+.wp-embed-share{margin-left:10px}.wp-embed-share-dialog{position:absolute;top:0;left:0;right:0;bottom:0;background-color:#1d2327;background-color:rgba(0,0,0,.9);color:#fff;opacity:1;transition:opacity .25s ease-in-out}.wp-embed-share-dialog.hidden{opacity:0;visibility:hidden}.wp-embed-share-dialog-close,.wp-embed-share-dialog-open{margin:-8px 0 0;padding:0;background:0 0;border:none;cursor:pointer;outline:0}.wp-embed-share-dialog-close .dashicons,.wp-embed-share-dialog-open .dashicons{padding:4px}.wp-embed-share-dialog-open .dashicons{top:8px}.wp-embed-share-dialog-close:focus .dashicons,.wp-embed-share-dialog-open:focus .dashicons{box-shadow:0 0 0 2px #2271b1;outline:2px solid transparent;border-radius:100%}.wp-embed-share-dialog-close{position:absolute;top:20px;right:20px;font-size:22px}.wp-embed-share-dialog-close:hover{text-decoration:none}.wp-embed-share-dialog-close .dashicons{height:24px;width:24px;background-size:24px}.wp-embed-share-dialog-content{height:100%;transform-style:preserve-3d;overflow:hidden}.wp-embed-share-dialog-text{margin-top:25px;padding:20px}.wp-embed-share-tabs{margin:0 0 20px;padding:0;list-style:none}.wp-embed-share-tab-button{display:inline-block}.wp-embed-share-tab-button button{margin:0;padding:0;border:none;background:0 0;font-size:16px;line-height:1.3;color:#a7aaad;cursor:pointer;transition:color .1s ease-in}.wp-embed-share-tab-button [aria-selected=true]{color:#fff}.wp-embed-share-tab-button button:hover{color:#fff}.wp-embed-share-tab-button+.wp-embed-share-tab-button{margin:0 0 0 10px;padding:0 0 0 11px;border-left:1px solid #a7aaad}.wp-embed-share-tab[aria-hidden=true]{display:none}p.wp-embed-share-description{margin:0;font-size:14px;line-height:1;font-style:italic;color:#a7aaad}.wp-embed-share-input{box-sizing:border-box;width:100%;border:none;height:28px;margin:0 0 10px;padding:0 5px;font-size:14px;font-weight:400;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;line-height:1.5;resize:none;cursor:text}textarea.wp-embed-share-input{height:72px}html[dir=rtl] .wp-embed-featured-image.square{float:right;margin-right:0;margin-left:20px}html[dir=rtl] .wp-embed-site-title a{padding-left:0;padding-right:35px}html[dir=rtl] .wp-embed-site-icon{margin-right:0;margin-left:10px;left:auto;right:0}html[dir=rtl] .wp-embed-meta{text-align:left}html[dir=rtl] .wp-embed-share{margin-left:0;margin-right:10px}html[dir=rtl] .wp-embed-share-dialog-close{right:auto;left:20px}html[dir=rtl] .wp-embed-share-tab-button+.wp-embed-share-tab-button{margin:0 10px 0 0;padding:0 11px 0 0;border-left:none;border-right:1px solid #a7aaad}
</style>
</head>
<body class="wp-singular post-template-default single single-post postid-618 single-format-standard wp-embed-responsive wp-theme-rockstead eio-default translatepress-en_US singular enable-search-modal has-post-thumbnail has-single-pagination showing-comments hide-avatars footer-top-visible elementor-default">
	<div class="wp-embed post-618 post type-post status-publish format-standard has-post-thumbnail hentry category-uncategorized-en">
		
		<p class="wp-embed-heading">
			<a href="single-vs-multi-families-office.html" target="_top">
				Single Versus Multi-Families Office Structure			</a>
		</p>

					<div class="wp-embed-featured-image square">
				<a href="single-vs-multi-families-office.html" target="_top">
					<img fetchpriority="high" width="300" height="179" src="static/picture/Classy-Office-300x179.jpg" class="attachment-medium size-medium" alt="" decoding="async" srcset="static/picture/Classy-Office-300x179.jpg 300w, static/picture/Classy-Office-18x12.jpg 18w, static/picture/Classy-Office.jpg 630w" sizes="(max-width: 300px) 100vw, 300px">				</a>
			</div>
		
		<div class="wp-embed-excerpt"><p>Depending on each family’s requirements, the family office fund structure could range from simple investment holding structures to complex arrangements involving multiple trusts, sub-trusts for individual beneficiaries, private trust companies, multiple funds, and complex asset classes, among others. In this paper, we will discuss two common structures, the Single Family Office (SFO) and Multi-family Office &hellip; <a href="single-vs-multi-families-office.html" class="wp-embed-more" target="_top">Continue reading <span class="screen-reader-text">Single Versus Multi-Families Office Structure</span></a></p>
</div>

		
		<div class="wp-embed-footer">
			<div class="wp-embed-site-title"><a href="index.html" target="_top"><img src="static/picture/cropped-rockstead-color-logo-32x32.png" srcset="static/picture/cropped-rockstead-color-logo-150x150.png 2x" width="32" height="32" alt="" class="wp-embed-site-icon"><span>Rockstead</span></a></div>
			<div class="wp-embed-meta">
					<div class="wp-embed-share">
		<button type="button" class="wp-embed-share-dialog-open" aria-label="Open sharing dialog">
			<span class="dashicons dashicons-share"></span>
		</button>
	</div>
				</div>
		</div>
	</div>
	<div class="wp-embed-share-dialog hidden" role="dialog" aria-label="Sharing options">
		<div class="wp-embed-share-dialog-content">
			<div class="wp-embed-share-dialog-text">
				<ul class="wp-embed-share-tabs" role="tablist">
					<li class="wp-embed-share-tab-button wp-embed-share-tab-button-wordpress" role="presentation">
						<button type="button" role="tab" aria-controls="wp-embed-share-tab-wordpress-618-1231241428" aria-selected="true" tabindex="0">WordPress Embed</button>
					</li>
					<li class="wp-embed-share-tab-button wp-embed-share-tab-button-html" role="presentation">
						<button type="button" role="tab" aria-controls="wp-embed-share-tab-html-618-1231241428" aria-selected="false" tabindex="-1">HTML Embed</button>
					</li>
				</ul>
				<div id="wp-embed-share-tab-wordpress-618-1231241428" class="wp-embed-share-tab" role="tabpanel" aria-hidden="false">
					<input type="text" value="/single-vs-multi-families-office/" class="wp-embed-share-input" aria-label="URL" aria-describedby="wp-embed-share-description-wordpress-618-1231241428" tabindex="0" readonly>

					<p class="wp-embed-share-description" id="wp-embed-share-description-wordpress-618-1231241428">
						Copy and paste this URL into your WordPress site to embed					</p>
				</div>
				<div id="wp-embed-share-tab-html-618-1231241428" class="wp-embed-share-tab" role="tabpanel" aria-hidden="true">
					<textarea class="wp-embed-share-input" aria-label="HTML" aria-describedby="wp-embed-share-description-html-618-1231241428" tabindex="0" readonly>&lt;blockquote class=&quot;wp-embedded-content&quot; data-secret=&quot;vwmqwWdGhH&quot;&gt;&lt;a href=&quot;/single-vs-multi-families-office/&quot;&gt;Single Versus Multi-Families Office Structure&lt;/a&gt;&lt;/blockquote&gt;&lt;iframe sandbox=&quot;allow-scripts&quot; security=&quot;restricted&quot; src=&quot;/single-vs-multi-families-office/embed/#?secret=vwmqwWdGhH&quot; width=&quot;600&quot; height=&quot;400&quot; title=&quot;&amp;#8220;Single Versus Multi-Families Office Structure&amp;#8221; &amp;#8212; Rockstead&quot; data-secret=&quot;vwmqwWdGhH&quot; frameborder=&quot;0&quot; marginwidth=&quot;0&quot; marginheight=&quot;0&quot; scrolling=&quot;no&quot; class=&quot;wp-embedded-content&quot;&gt;&lt;/iframe&gt;&lt;script&gt;
/*! This file is auto-generated */
!function(d,l){&quot;use strict&quot;;l.querySelector&amp;&amp;d.addEventListener&amp;&amp;&quot;undefined&quot;!=typeof URL&amp;&amp;(d.wp=d.wp||{},d.wp.receiveEmbedMessage||(d.wp.receiveEmbedMessage=function(e){var t=e.data;if((t||t.secret||t.message||t.value)&amp;&amp;!/[^a-zA-Z0-9]/.test(t.secret)){for(var s,r,n,a=l.querySelectorAll(&#039;iframe[data-secret=&quot;&#039;+t.secret+&#039;&quot;]&#039;),o=l.querySelectorAll(&#039;blockquote[data-secret=&quot;&#039;+t.secret+&#039;&quot;]&#039;),c=new RegExp(&quot;^https?:$&quot;,&quot;i&quot;),i=0;i&lt;o.length;i++)o[i].style.display=&quot;none&quot;;for(i=0;i&lt;a.length;i++)s=a[i],e.source===s.contentWindow&amp;&amp;(s.removeAttribute(&quot;style&quot;),&quot;height&quot;===t.message?(1e3&lt;(r=parseInt(t.value,10))?r=1e3:~~r&lt;200&amp;&amp;(r=200),s.height=r):&quot;link&quot;===t.message&amp;&amp;(r=new URL(s.getAttribute(&quot;src&quot;)),n=new URL(t.value),c.test(n.protocol))&amp;&amp;n.host===r.host&amp;&amp;l.activeElement===s&amp;&amp;(d.top.location.href=t.value))}},d.addEventListener(&quot;message&quot;,d.wp.receiveEmbedMessage,!1),l.addEventListener(&quot;DOMContentLoaded&quot;,function(){for(var e,t,s=l.querySelectorAll(&quot;iframe.wp-embedded-content&quot;),r=0;r&lt;s.length;r++)(t=(e=s[r]).getAttribute(&quot;data-secret&quot;))||(t=Math.random().toString(36).substring(2,12),e.src+=&quot;#?secret=&quot;+t,e.setAttribute(&quot;data-secret&quot;,t)),e.contentWindow.postMessage({message:&quot;ready&quot;,secret:t},&quot;*&quot;)},!1)))}(window,document);
&lt;/script&gt;
</textarea>

					<p class="wp-embed-share-description" id="wp-embed-share-description-html-618-1231241428">
						Copy and paste this code into your site to embed					</p>
				</div>
			</div>

			<button type="button" class="wp-embed-share-dialog-close" aria-label="Close sharing dialog">
				<span class="dashicons dashicons-no"></span>
			</button>
		</div>
	</div>
	<script>
/*! This file is auto-generated */
!function(c,u){"use strict";var r,t,e,a=u.querySelector&&c.addEventListener,f=!1;function b(e,t){c.parent.postMessage({message:e,value:t,secret:r},"*")}function m(){b("height",Math.ceil(u.body.getBoundingClientRect().height))}function n(){if(!f){f=!0;var e,r=u.querySelector(".wp-embed-share-dialog"),t=u.querySelector(".wp-embed-share-dialog-open"),a=u.querySelector(".wp-embed-share-dialog-close"),n=u.querySelectorAll(".wp-embed-share-input"),i=u.querySelectorAll(".wp-embed-share-tab-button button"),s=u.querySelector(".wp-embed-featured-image img");if(n)for(e=0;e<n.length;e++)n[e].addEventListener("click",function(e){e.target.select()});if(t&&t.addEventListener("click",function(){r.className=r.className.replace("hidden",""),u.querySelector('.wp-embed-share-tab-button [aria-selected="true"]').focus()}),a&&a.addEventListener("click",function(){o()}),i)for(e=0;e<i.length;e++)i[e].addEventListener("click",d),i[e].addEventListener("keydown",l);u.addEventListener("keydown",function(e){var t;27===e.keyCode&&-1===r.className.indexOf("hidden")?o():9===e.keyCode&&(e=e,t=u.querySelector('.wp-embed-share-tab-button [aria-selected="true"]'),a!==e.target||e.shiftKey?t===e.target&&e.shiftKey&&(a.focus(),e.preventDefault()):(t.focus(),e.preventDefault()))},!1),c.self!==c.top&&(m(),s&&s.addEventListener("load",m),u.addEventListener("click",function(e){var t=((t=e.target).hasAttribute("href")?t:t.parentElement).getAttribute("href");event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||t&&(b("link",t),e.preventDefault())}))}function o(){r.className+=" hidden",u.querySelector(".wp-embed-share-dialog-open").focus()}function d(e){var t=u.querySelector('.wp-embed-share-tab-button [aria-selected="true"]');t.setAttribute("aria-selected","false"),u.querySelector("#"+t.getAttribute("aria-controls")).setAttribute("aria-hidden","true"),e.target.setAttribute("aria-selected","true"),u.querySelector("#"+e.target.getAttribute("aria-controls")).setAttribute("aria-hidden","false")}function l(e){var t,r=e.target,a=r.parentElement.previousElementSibling,n=r.parentElement.nextElementSibling;if(37===e.keyCode)t=a;else{if(39!==e.keyCode)return!1;t=n}(t="rtl"===u.documentElement.getAttribute("dir")?t===a?n:a:t)&&(e=t.firstElementChild,r.setAttribute("tabindex","-1"),r.setAttribute("aria-selected",!1),u.querySelector("#"+r.getAttribute("aria-controls")).setAttribute("aria-hidden","true"),e.setAttribute("tabindex","0"),e.setAttribute("aria-selected","true"),e.focus(),u.querySelector("#"+e.getAttribute("aria-controls")).setAttribute("aria-hidden","false"))}}a&&(!function e(){c.self===c.top||r||(r=c.location.hash.replace(/.*secret=([\d\w]{10}).*/,"$1"),clearTimeout(t),t=setTimeout(function(){e()},100))}(),u.documentElement.className=u.documentElement.className.replace(/\bno-js\b/,"")+" js",u.addEventListener("DOMContentLoaded",n,!1),c.addEventListener("load",n,!1),c.addEventListener("resize",function(){c.self!==c.top&&(clearTimeout(e),e=setTimeout(m,100))},!1),c.addEventListener("message",function(e){var t=e.data;t&&e.source===c.parent&&(t.secret||t.message)&&t.secret===r&&"ready"===t.message&&m()},!1))}(window,document);
</script>
	<script>
	/(trident|msie)/i.test(navigator.userAgent)&&document.getElementById&&window.addEventListener&&window.addEventListener("hashchange",function(){var t,e=location.hash.substring(1);/^[A-z0-9_-]+$/.test(e)&&(t=document.getElementById(e))&&(/^(?:a|select|input|button|textarea)$/i.test(t.tagName)||(t.tabIndex=-1),t.focus())},!1);
	</script>
	</body>
</html>
