﻿/*==============================
======== start common css ===========
==============================*/

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@font-face {
    font-family: BemboStd;
    src: url(/wp-content/themes/rockstead/assets/fonts/BemboStd.otf);
}
@font-face {
    font-family: NeoSansStd-Regular;
    src: url(/wp-content/themes/rockstead/assets/fonts/Neo-Sans-Std-Regular.otf);
}
@font-face {
    font-family: NeoSansStd-Bold;
    src: url(/wp-content/themes/rockstead/assets/fonts/Neo-Sans-Std-Bold.otf);
}
@font-face {
    font-family: NeoSansStd-Light;
    src: url(/wp-content/themes/rockstead/assets/fonts/Neo-Sans-Std-Light.otf);
}
@font-face {
    font-family: NeoSansStd-Medium;
    src: url(/wp-content/themes/rockstead/assets/fonts/Neo-Sans-Std-Medium.otf);
}
h1,h2,h3,h4,h5,h6{
    font-family: BemboStd;
    color: #1A1A1A;
}
p{
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;   
    line-height: 26px; 
}
a{
    text-decoration: none !important;
}

section{
    position: relative;
}

.common_button {
    font-size: 12px;
    font-family: NeoSansStd-Regular;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    background: #1F386D;
    border: 1px solid #1F386D;
    padding: 13px 30px;
    text-decoration: none !important;
    transition: .5s;
    display: inline-block;
}
.common_button:hover{
    margin-left: 20px;  
    color: #fff;
    transition: .5s all;
}
.coomon_btn_2 {
    font-family: NeoSansStd-Regular;
    color: #C6962E;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.15em;
    line-height: 26px;
}
.coomon_btn_2 i {
    font-size: 12px;
    margin-left: 8px;
}

.content_center{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
ul{
    list-style: none;
}
.pad_t{
    padding-top: 5rem;
}
.pad_b{
    padding-bottom: 5rem;
}
.pad_tb{
    padding: 5rem 0;
}
.mr_t{
    margin-top: 5rem;
}
.mr_b{
    margin-bottom: 5rem;
}
.mr_tb{
    margin: 5rem 0;
}
.flex-direction-row-reverse {
    flex-direction: row-reverse;
}
.common_heading{
    text-align: center;
    margin: 2rem 0;
}
.common_heading h2 {
    font-family: BemboStd;
    font-weight: normal;
    font-size: 40px;
    color: #1a1a1a;
    margin-bottom: 7px;
    text-transform: capitalize;
}
.common_heading .small_heading {
    line-height: 15px;
    font-family: NeoSansStd-Bold;
    font-weight: bold;
    font-size: 12px;
    letter-spacing: 0.1em;
    color: #c6962e;
    text-transform: uppercase;
}
:focus-visible {
    outline: 0;
}
:focus {   
    box-shadow: none !important;
}

/* owl clider css */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
    display: block;
}
.owl-theme .owl-nav button ,.brands-slider.owl-theme .owl-nav button{    
    width: 47px;
    height: 47px;
    background: transparent !important;
    border: 1px solid #c6962e !important;
    line-height: 25px;
    outline: none;
    border-radius: 100% !important;
    margin-bottom: 12px !important;
    color: #c6962e !important;
    transition: .4s all;
}

.owl-theme .owl-nav [class*=owl-]:hover {   
    color: #fff !important;
    background: #c6962e !important;
    text-decoration: none;
}
.owl-theme .owl-nav button.owl-prev {
    left: -25px;
}
.owl-theme .owl-nav button.owl-next {
    right: -25px;
}

/*==============================
======== close common css ===========
==============================*/

/*==============================
======== main header css===========
==============================*/
header.main-header {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    top: 0;
    z-index: 99;
}
header.main-header nav {
    padding: 0;
    position: relative;
    z-index: 99;
}
.main-header::before {
    content: '';
    content: "";
    display: inline-block;
    width: 100%;
    height: 0;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #222b40;
    z-index: 1;
    transition: height .3s;
}
.headerbg::before{
    height: 95px;
}
.logo-div img {
    width: 180px;
}
.logo-div {
    padding-top: 5px;
}
.header_icons {
    position: absolute;
    right: 0%;
    display: flex;
    align-items: center;
}
.serach_icon {
    margin-right: 1rem;
}
.serach_icon i {
    cursor: pointer;
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    line-height: 1;
}
.navbar .open-menu button {
    display: inline;
    z-index: 0;
    background: transparent;
    border: none;
    margin-top: 0;
    margin-left: 0;
    padding: 0px;
    cursor: pointer;
    outline: 0px !important;
}
.hamburger .line {
    width: 32px;
    height: 2px;
    background-color: #fff;
    display: block;
    margin: 8px auto;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.menubarbox {
    height: 100vh;
    width: 100%;
    position: absolute;
    left: 0;
    z-index: 5;
    background: #2e3a57;
    overflow: hidden;
    text-align: left;
    -webkit-transition: -webkit-transform 250ms ease-in-out;
    transition: transform 250ms ease-in-out;
    top: -100vh;
}
.headermenu-box {
    height: 100vh;
    justify-content: center;
    display: flex;
    flex-direction: column;
    padding-bottom: 15px;
    width: 300px;
    margin: 0 auto;
    margin-right: 0;
}
.headermenu h2 {
    font-family: BemboStd;
    text-transform: uppercase;
    font-size: 22px;
    color: #fff;
    letter-spacing: 1.5px;
    padding-top: 3rem;
    padding-bottom: 5px;
}
.headermenu ul li {
    margin: 10px 0px;
    padding-top: 1rem;
}
.headermenu ul li a {
    font-family: 'NeoSansStd-Regular';
    color: #f2f4f3;
    font-size: 26px;
    font-weight: 200;
    letter-spacing: 1px;
}
.menubarbox::after {
    content: "";
    display: block;
    width: calc(100% + 80px);
    position: absolute;
    top: 0px;
    left: 0;
    opacity: 0.5;
/*     background-image: url(../images/headerbg.svg); */
    background-position: 0;
    background-repeat: no-repeat;
    background-size: auto 100%;
    transition: transform 0s .25s,opacity .25s 0s;
    height: 100vh;
    z-index: -1;
}
.menubarbox-bottombar {
    -webkit-transform: translateX(100vh);
    -moz-transform: translateX(100vh);
    -o-transform: translateX(100vh);
    -ms-transform: translateX(100vh);
    transform: translateY(100vh);
}
#hamburger-6.is-active .line:nth-child(1) {
    -webkit-transform: translateY(8px) rotate(52deg);
    -ms-transform: translateY(8px) rotate(52deg);
    -o-transform: translateY(8px) rotate(52deg);
    transform: translateY(8px) rotate(52deg);
}
#hamburger-6.is-active .line:nth-child(2) {
    width: 0px;
}
#hamburger-6.is-active .line:nth-child(3) {
    -webkit-transform: translateY(-13px) rotate(126deg);
    -ms-transform: translateY(-13px) rotate(126deg);
    -o-transform: translateY(-13px) rotate(126deg);
    transform: translateY(-13px) rotate(126deg);
}
#hamburger-6.is-active .line:nth-child(1), #hamburger-6.is-active .line:nth-child(3) {
    -webkit-transition-delay: 0.3s;
    -o-transition-delay: 0.3s;
    transition-delay: 0.3s;
}



/*==============================
======== main_banner css===========
==============================*/

.main_banner_section {
    position: relative;
    height: 100vh;
    background-position: center;
    /* background-size: 100% 100%; */
    background-size: cover;
    background-repeat: no-repeat;
}
.banner_content_wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    width: 80%;
    text-align: center;
}
.banner_content_wrap h1 {
    position: relative;
    font-family: BemboStd;
    font-size: 80px;
    line-height: 67px;
    color: #fff;
    text-transform: capitalize;
}
.common_overlay{
    position: relative;
    overflow: hidden;
}
.common_overlay:hover::before {
    transform: translateY(-100%);
}
.common_overlay::before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #c6962e4a;
    z-index: 1;
    transform: translate(0);
    transition: transform .25s;
}
#myVideo {
    position: relative;
    width: 100%;
    height: 100vh;
    object-fit: cover;
}



/*==============================
======== Banner Bootom===========
==============================*/
.banner_bottom_section {
    margin-top: 1rem;
}
.banner_bottom_card_wrap {
    cursor: pointer;
    background: #F1F1F1;
    padding: 20px;
    height: 140px;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    margin: 1rem 0;
}
.banner_bottom_card_wrap p {
    font-family: NeoSansStd-Regular;
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    color: #646464;
    margin-bottom: 0;
}
.banner_bottom_card_wrap h5 {
    font-family: BemboStd;
    font-weight: normal;
    font-size: 26px;
    text-align: left;
    margin-bottom: 5px;
    text-transform: capitalize;
}
.our_brif_card {
    background: #1f386ded;
}
.our_brif_card p,.our_brif_card h5 {
    color: #fff;
}

/*==============================
======== about us===========
==============================*/
.about_content_wrap h3 {
    font-family: NeoSansStd-Bold;
    font-size: 20px;
    letter-spacing: 0.1em;
    line-height: 24px;
    text-transform: uppercase;
    color: #c6962e;
    margin-bottom: 14px;
}
.about_content_wrap {
    padding: 0 2rem;   
}
.about_left_img img {
    width: 100%;
    transition: .5s all;
    height: 100%;
    object-fit: cover;
}
.about_left_img{
    position: relative; 
    height: 100%;
}
.img_zoom{
    overflow: hidden;   
}
.about_left_img .img_zoom {
    height: 100%;
}
.img_zoom img:hover{
    transform: scale(1.2);
}
.about_left_img::before {
    content: "";
    background-image: url(../image/repeat-yellow-grid.png);
    background-repeat: no-repeat;
    position: absolute;
    left: -37px;
    top: -37px;
    width: 76px;
    height: 76px;
} 


/*==============================
======== Our Journey Css ===========
==============================*/

.our_journey_section {
    padding-bottom: 5rem;
}

.our_journey_bg {
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
    padding: 5rem 0;
}
.our_journey_bg:before {
    content: '';
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background: rgb(29 40 2 / 40%);
}
.our_journey_card {
    position: relative;
    background: #fff;
    padding: 20px 30px;
    width: 100%;
    height: 170px;
}
.our_journey_card h4 {
    font-family: NeoSansStd-Bold;
    font-size: 38px;
    display: inline-block;
    letter-spacing: 0.1em;
    line-height: 24px;
    text-transform: uppercase;
    color: #c6962e;
    margin-bottom: 14px;
    position: relative;
}
.our_journey_card h4:before {
    content: '';
    background: #c6962e;
    position: absolute;
    left: -15px;
    top: -5px;
    display: block;
    height: 45px;
    width: 5px;
}

/*==============================
======== our core value Css ===========
==============================*/

.our_value_card_wrap {
    background: #fff;
    box-shadow: 0px 30px 40px rgba(0, 0, 0, 0.05);
    margin: 3rem 0;
    padding: 2rem;
}
.img_wrap {
    margin-bottom: 1.5rem;
}
.img_wrap img{
   width: 60px !important;
}
.our_value_deatil h4 {
    font-family: BemboStd;
    font-size: 22px;
    line-height: 29px;
    color: #1a1a1a;
    position: relative;
    padding-bottom: 15px;
}
.our_value_deatil p {
    font-family: NeoSansStd-Light;
    font-size: 14px;
    line-height: 22px;
}
.our-value-slider .owl-nav {
    position: absolute;
    top: -45%;
    right: 11%;
}
.our_priority_slider_wrap{
    margin-top: 3rem;
    margin-right: 0rem;
    margin-left: 4rem;
}
.our_value_deatil h4::after {
    content: "";
    position: absolute;
    border-bottom: 2px solid #C6962E;
    left: 0;
    bottom: 0;
    width: 50px;
}
.our_value_slider_btn{
    text-align: center;
    margin: 2rem 0;
}
.core_value_section::before {
    content: "";
    background-image: url(../image/clipart-black-dot-background.png);
    background-repeat: no-repeat;
    position: absolute;
    left: 50%;
    top: 60%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.our_edge_content_wrap {
    background: #fff;
    padding: 2rem 3rem;
    position: relative;
    left: 12%;
    bottom: 0;
    z-index: 5;
    box-shadow: 0px 15px 30px rgb(0 0 0 / 16%);
    height: auto;
    margin: auto;
}
.our_edge_points li {
    display: flex;
    align-items: flex-start;
}
.our_edge_points li img {
    margin-right: 10px;
}

/*==============================
======== history timeline Css ===========
==============================*/
.history_timeline_card {
    display: inline-flex;
    align-items: baseline;
    justify-content: space-between;
    text-align: end;
    width: 100%;
    margin: 16px 0;
    transition: .5s all;
}

.content_row_wrap {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 88%;
}
.timeline_content h6 {
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #c6962e;
    text-transform: capitalize;
}
.timeline_content ul li {
    font-family: NeoSansStd-Regular;
    font-size: 14px;
    line-height: 23px;
    color: #646464;    
}
.timeline_content {
    width: 56%;
}
.year_circle {
    font-family: NeoSansStd-Regular;
    font-size: 15px;
    text-align: center;
    color: #1a1a1a;
    width: 80px;
    height: 80px;
    background: #fff;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    filter: drop-shadow(0px 6px 32px rgba(0, 0, 0, 0.2));
    transition: .5s all;
}
.history_timeline_card:hover .year_circle{
    background: #1F386D;
    filter: none;
    color: #fff;
}
.timeline_join_row {
    width: 10em;
    display: block;
    height: 1px;
    background-color: #C6962E;
    margin-bottom: auto;
    margin-top: 10px;
    position: relative;
}
.timeline_join_row::before,.timeline_join_row::after{
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;    
    margin: auto;
    width: 10px;
    height: 10px;
    border-radius: 100%;
    background: #C6962E;
}
.timeline_join_row::before{
    left: 0;
}
.timeline_join_row::after{   
    right: 0;
}
.history_timeline_wrap .history_timeline_card_wrap:nth-child(even) .history_timeline_card  {
    flex-direction: row-reverse;
} 
.history_timeline_wrap .history_timeline_card_wrap:nth-child(even) .history_timeline_card .content_row_wrap  {
    flex-direction: row-reverse;
} 
.history_timeline_wrap .history_timeline_card_wrap:nth-child(even) .history_timeline_card .content_row_wrap .timeline_content {
    text-align: start;
} 
.history_timeline .yellow_grid {
    position: absolute;
    top: 13%;
    left: 0;
}
.clipart_bg {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: -1;
}

/*==============================
======== home_contact_section Css ===========
==============================*/

.home_contact_section {
    background: #EFEFEF;
}
.overlap_img_wrap {
    position: relative;
    overflow: hidden;
}
.overlap_first_img {
    float: right;
    position: relative;
    top: 40px;
    right: 0px;
    z-index: 2;
}
.overlap_second_img {
    position: relative;
    bottom: 40px;
    left: 20px;
    z-index: 0;
}
.home_conatct_form_wrap {
    padding: 0 3rem;
}
.form-control {
    font-family: NeoSansStd-Regular;
    font-size: 12px;
    line-height: 26px;
    color: #646464;
    height: 45px;
    background-color: #fff;
    border: 0;
    border-radius: 0;
    text-transform: capitalize;
    padding: 0 20px;
	margin-bottom: 15px;
}
textarea.form-control {
    height: 104px;
}
/*==============================
======== Footer Css ===========
==============================*/

footer.footer_section {
    background: #000;
    color: #fff;
    padding-top: 80px;
}
.footer_links h3 {
    font-family: BemboStd;
    font-size: 12px;
    letter-spacing: 0.15em;
    line-height: 24px;
    color: #fff;
    text-transform: uppercase;
}
.footer_links ul {
	padding: 0;
}
.footer_links {
    margin: 1rem auto;
    /*max-width: 50%;*/
    text-align: center;
}
.footer_links p {
	font-family: NeoSansStd-Light;
	color: #fff;
	font-size: 14px;
	line-height: 24px;
	width: 100%;
}
.footer-newslatter input {
	background: transparent;
	border: 0;
	border-bottom: 1px solid #fff;
	width: 75%;
	color: #fff;
    outline: 0;
    padding: 10px;
}

.footer-newslatter input::placeholder {
	font-family: NeoSansStd-Light;
	font-weight: 500;
	font-size: 12px;
	line-height: 24px;
	color: #fff;
}
.footer-newslatter {
	display: inline;
	position: relative;
}
.footer-newslatter button {
	background: transparent;
	border: 0;
	position: absolute;
	top: 0;
	right: 20px;
}
.footer-newslatter button i {
	color: #fff;
}
.footer-newslatter button i:hover {
	color: #C6962E;
}
.footer_links ul li a ,.footer_links ul li {
	font-family: NeoSansStd-Light;
	font-size: 14px;
	line-height: 24px;
	color: #fff;
    margin: 3px 0;
    transition: .5s all;
}
.footer_links ul li a:hover {
    color: #C6962E;
    padding-left: 10px;
}
.copyright_section p,.copyright_section a {
	font-family: NeoSansStd-Light;
	font-size: 14px;
	line-height: 24px;
	color: #fff;
	margin-bottom: 0;
}
.footer_social_icon ul li {
	display: inline;
}
.footer_social_icon ul li a i {
	border: 1px solid #fff;
	width: 44px;
	height: 44px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border-radius: 100%;
	margin-right: 5px;
    font-size: 20px;
    transition: .5s all;
}
.footer_social_icon ul li a i:hover{
    background: transparent;
    color: #C6962E;
    border-color: #C6962E;
}
.footer_social_icon {
    text-align: right;    
}
.copyright_section {
	border-top: 1px solid #ffffff61;
	padding-top: 2rem;
	margin-top: 2rem;
}

/*==============================
======== inner banner section css ===========
==============================*/

.inner_banner_section::before{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(rgb(0,0,0,.5), rgba(0,0,0,0.5));
    display: block;
    height: 100%;
    width: 100%;
}
.inner_banner_section {
    height: 45vh;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 100% 100%;
    background-attachment: fixed;
}
.inner_banner_content {
    z-index: 1;
}
.inner_banner_content h2 {
    font-size: 45px;
    font-family: Montserrat-Bold;
    color: #fff;
    line-height: 58px;
    text-transform: capitalize;
}

.inner_banner_content p {
    font-size: 18px;
    line-height: 18px;
    color: #fff;
    text-transform: capitalize;
    font-family: Montserrat-SemiBold;
}

/*====================================
========Our Management Team css ===========
======================================*/
.inner_faq_section {
    background: #EDF7F9;
}
.team_member_detail {
    text-align: center;
    padding: 10px;
    background-color: #fff;
}
.team_member_detail h4 {
    font-family: NeoSansStd-Bold;
    font-size: 19px;
    color: #c6962e;
    line-height: 18px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    margin-bottom: 16px;
}
.team_member_detail h6 {
    font-family: NeoSansStd-Regular;
    font-size: 15px;
    color: #1a1a1a;
    line-height: 15px;
    margin: 0;
    letter-spacing: 0.1em;
    text-transform: capitalize;
}

.social_icon li a i {
    border: 1px solid #fff;
    width: 44px;
    height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    margin-right: 5px;
    font-size: 20px;
    transition: .5s all;
}

.team-member {
    margin: 15px 0;
    padding: 0;
    box-shadow: rgb(149 157 165 / 20%) 0px 8px 24px;
    width: 90%;
    margin: 0 auto;
    display: flex;
    margin-bottom: 2rem;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background: #fff;
}
  
  .team-member figure {
    position: relative;
    overflow: hidden;
    padding: 0;
    margin: 0;
  }
  .team-member button {
        border: 0;
        width: 100%;
    }
  .team-member figure img {
    min-width: 100%;
  }
  
  .team-member figcaption p {
    font-size: 16px;
    font-weight: 600;
  }
  
  .team-member figcaption ul {
    list-style: none;
    margin: 0;
    padding: 0;
    text-align: center;
  }
  
  .team-member figcaption ul {
    visibility: visible;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
  }
  
  .team-member figcaption ul li {
    display: inline-block;
    padding: 10px;
  } 
.team-member figcaption {
    padding: 0 30px;
    color: transparent;
    background-color: transparent;
    position: absolute;
    z-index: 996;
    bottom: -60%;
    left: 0;
    width: 100%;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
    
.team-member:hover .img-overlay{
    visibility: visible;
    height: 15%;   
    bottom: 0;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.team-member:hover figcaption {
    visibility: visible;
    color: #fff;
    background: #c6962e75;
    height: 15%;    
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
  
.team-member:hover figcaption ul li a:hover {
    color: rgba(49, 49, 49, .97);
}
  
.team-member figure img {
    -webkit-transform: scale(1) rotate(0) translateY(0);
    -moz-transform: scale(1) rotate(0) translateY(0);
    -o-transform: scale(1) rotate(0) translateY(0);
    -ms-transform: scale(1) rotate(0) translateY(0);
    transform: scale(1) rotate(0) translateY(0);
    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}
  
.team-member:hover img {
    -webkit-transform: scale(1.1) rotate(1deg) translateY(12px);
    -moz-transform: scale(1.1) rotate(1deg) translateY(12px);
    -o-transform: scale(1.1) rotate(1deg) translateY(12px);
    -ms-transform: scale(1.1) rotate(1deg) translateY(12px);
    transform: scale(1.1) rotate(1deg) translateY(12px);
    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}
.team-member figcaption ul li a i {
    color: #fff;
    font-size: 24px;
}
.team-member figure {
    width: 100%;
    height: 275px;
}

.team-member figure img {
    width: 100%;
    /*height: 100%;
    object-fit: cover;*/
    transform: scale(1.1);
    transition: 0.5s all;
}
/*====================================
========  Our Relationship Managers section css===========
======================================*/
.realtion_team_member figure {
    border-radius: 100%;
    width: 200px;
    height: 200px;
    overflow: hidden;
    margin: auto;
    border: 1px solid #c6962e;
    box-shadow: rgb(198 150 46 / 13%) 0px 8px 24px;
}
.realtion_team_member figure img {
    width: 100%;
    /*height: 100%;
    object-fit: cover;*/
    transform: scale(1.1);
    transition: 0.5s all;
}
.realtion_team_member {
    margin-bottom: 1rem;
}

.team_member_name h4 {
    font-family: NeoSansStd-Bold;
    font-size: 19px;
    color: #c6962e;
    line-height: 18px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    margin-bottom: 16px;
    text-align: center;
    margin: 1rem 0;
}
/*====================================
========  Team filter section css===========
======================================*/
.team_member_filter{
    margin-bottom: 2rem;
}
.member_search .has-search {
    position: relative;
}
.member_search .has-search .form-control-feedback {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    height: 50px;
    line-height: 3rem;
    color: #0A2B45;
    padding-right: 10px;
    display: inline-block;
}
.member_search input {
    width: 100%;
    padding: 10px;
    margin: 0 1rem;
    border: 0;
    border-radius: 0;
    height: 50px;
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    text-transform: uppercase;
}
.team_select {
    display: flex;
    width: 65%;
}
.member_search {
    width: 80%;
    margin-left: auto;
}

.team_select .form-select {
    margin: 0 1rem;
}
/*====================================
========  Accordian section css===========
======================================*/
.contact_accordion .accordion-button::after {
    content: "\2b";
    font-size: 20px;
    font-family: 'FontAwesome';
    line-height: 10px;
    margin-left: auto;
    background: #1F386D;
    border: 1px solid #1F386D;
    color: #fff;
    border-radius: 100%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: none;
    background-repeat: no-repeat;
    background-size: 1.25rem;
    transition: transform .2s ease-in-out;    
}
.contact_accordion .accordion-button:not(.collapsed)::after{
    content: "\f068";
    font-family: 'FontAwesome';    
    background-image: none;
}
.contact_accordion .accordion-item {
    background: transparent;
    border: 0;
    border-bottom: 1px solid #d5d5d5;
}
.contact_accordion .accordion-button:not(.collapsed) {
    background: transparent;
}
.contact_accordion .accordion-button, .contact_accordion .accordion-button:not(.collapsed) {
    background: transparent;
    font-family: BemboStd;
    font-weight: normal;
    font-size: 40px;
    color: #c6962e;
    padding: 2rem 0;
    margin-bottom: 7px;
}
.team_select .form-select {
    margin: 0 1rem;
    border: 0;
    border-radius: 0;
    height: 50px;
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    line-height: 26px;
    text-transform: uppercase;
}

.contact_accordion .always_open_accordian .accordion-button::after{
    display: none;
}


/*====================================
========  modal section css===========
======================================*/
.single_managment_team_wrap .member_img_detail_wrap {
    box-shadow: rgb(149 157 165 / 20%) 0px 8px 24px;
    padding: 20px 0;
}
.single_managment_team_wrap .member_img_detail_wrap .member_img_wrap {
    width: 100%;
    height: 240px;
    overflow: hidden;
}
.single_managment_team_wrap {
    padding: 1rem 0;
}
.single_managment_team_wrap .member_img_detail_wrap .member_img_wrap img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.single_managment_team_wrap .detail_wrap{
    text-align: center;
    padding: 16px 16px 0;
}
.single_managment_team_wrap .member_img_detail_wrap .detail_wrap h4 {
    font-family: NeoSansStd-Bold;
    font-size: 19px;
    color: #c6962e;
    line-height: 18px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    margin-bottom: 16px;
}
.single_managment_team_wrap .member_img_detail_wrap .detail_wrap h6 {
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #1a1a1a;
    line-height: 18px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}
.single_managment_team_wrap .member_desc li {
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    line-height: 26px;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
}
ul.member_desc li i {
    color: #c6962e;
    line-height: 26px;
    margin-right: 10px;
}

.single_managment_team_wrap ul li {position: relative;margin-bottom: 8px;}

.single_managment_team_wrap ul li:after {content: "";
    background-image: url(../image/edge-point.png);
    position: absolute;
    top: 7px;
    left: -7%;
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;background-size: contain;
}
.modal-dialog.team-modal {
    max-width: 70%;
}
.modal-dialog.team-modal .single_managment_team_wrap .member_img_detail_wrap .member_img_wrap img{
    width: auto;
}
.modal-dialog.team-modal .single_managment_team_wrap .member_img_detail_wrap .member_img_wrap{
    height: 280px;
    text-align: center;
}

/*====================================
========  contact form section css===========
======================================*/
/*.contact_form_section {
    background: #EFEFEF;
}*/
.get_touch_side{
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 100%;
}
.get_touch_wrap {
    position: relative;
    display: flex;
    align-items: flex-start;
    padding: 2rem 0;
    width: 70%;
    margin: 0 auto;
}
.get_touch_side::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(rgb(0,0,0,.5), rgba(0,0,0,0.5));
    display: block;
    height: 100%;
    width: 100%;
}
.get_touch_wrap h5 {
    font-family: BemboStd;
    font-weight: 700;
    font-size: 20px;
    letter-spacing: 0.1em;
    line-height: 16px;
    margin-bottom: 16px;
    text-transform: uppercase;
    color: #c6962e;
}
.get_touch_wrap p {
    font-family: NeoSansStd-Regular;
    font-size: 14px;
    letter-spacing: 0.1em;
    line-height: 24px;
    text-transform: uppercase;
    color: #fff;
    margin-bottom: 14px;
}
.get_touch_wrap i {
    font-size: 20px;
    color: #c6962e;
    margin-right: 2rem;
}
.contact_form_fileds {
    background-color: #fff;
    padding: 2rem 2rem;
    box-shadow: 1px 1px 15px 1px rgb(0 0 0 / 15%);
}
.contact_form_fileds .form-control {
    border: 1px solid #e6e6e6;
}
.contact_form_fileds .form-label {
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    line-height: 26px;
    margin-bottom: 1rem;
}
.contact_form_btn{
    margin-top: 1rem;
}
.map_section iframe {
    width: 100%;
    height: 400px;
}

/* Inner top heading */
.inner_top_haed {
    text-align: center;
    padding-top: 6rem;
}
.inner_top_haed h3 {
    font-family: NeoSansStd-Bold;
    font-size: 40px;
    letter-spacing: 0.1em;
    line-height: 24px;
    text-transform: uppercase;
    color: #c6962e;
    margin-bottom: 14px;
}

/*asffsf*/

.formSearch .modal-dialog {
    max-width: 75%;
}
.formSearch .modal-content {
    background: transparent;
    border: 0;
}
.formSearch .modal-body {
    padding: 20px;
    background: #00000080;
    text-align: left;
}
.formSearch .modal-body h1{color: #fff;text-align: left;}
.formSearch .wp-block-search__inside-wrapper {
    display: flex;
}
.formSearch .second-btn {
    border-radius: 0 15px 15px 0;
    border: 0;
}
.formSearch .form-control {
    border-radius: 15px 0px 0px 15px;
    height: 50px;
    border: 1px solid #fff;
}
.formSearch .form-control:focus{
    outline: 0;
    box-shadow: none;
}
.formSearch .modal-header button {
    position: absolute;
    top: 0px;
    right: -15px;
    color: #fff !important;
    z-index: 99;
    font-size: 40px;
    background: no-repeat;
    border: 0;
}
.formSearch input#wp-block-search__input-1 {
    width: 87%;
}
.formSearch .wp-block-search__button{
    margin-left: 14px;
}
.formSearch label.wp-block-search__label {
    display: none;
}
.fund_and_exter_manage_content {
    background: #fff;
    padding: 30px;
    position: relative;    
    z-index: 5;
    box-shadow: 0px 15px 30px rgb(0 0 0 / 16%);
    height: auto;
}
.left_content{
    left: 10%;
}
.right_content{
    right: 10%;
}
.fund_and_exter_manag_img {
    box-shadow: rgb(149 157 165 / 20%) 0px 8px 24px;
    height: 500px;
    width: 100%;
}
.fund_and_exter_manag_img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.fund_and_exter_manage_content h4 {
    font-family: NeoSansStd-Bold;
    font-size: 20px;
    letter-spacing: 0.1em;
    line-height: 24px;
    text-transform: uppercase;
    color: #c6962e;
    margin-bottom: 14px;
}
.fund_and_exter_manage_content ul li {
    position: relative;
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    line-height: 20px;
    margin-bottom: 14px;
}
.fund_and_exter_manage_content ul li::before {
    content: "";
    background-image: url(../image/edge-point.png);
    position: absolute;
    top: 0;
    left: -7%;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
}
.fund_extren_managment_content_img {
    padding-bottom: 4rem;
}

/*All fund s addd*/

.all_funds_add {
    padding: 3rem 0;
    margin-top: 5rem;
}
.content_center li {
    position: relative;
    font-family: NeoSansStd-Regular;
    font-size: 16px;
    color: #646464;
    line-height: 20px;
    margin-bottom: 14px;
}
.content_center li::before {
    content: "";
    background-image: url(../image/edge-point.png);
    position: absolute;
    top: 0;
    left: -7%;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
}
.external_assets_managment, .funds_managment {
    padding: 3rem 0;
}
.external_assets_managment{
    padding: 0 0 3rem 0;
}
.famili_office_service_img img{
    width: 100%;
}

.insight_card_wrap {
    position: relative;
    background-position: center;
    background-size: 100% 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #2D3956;
    width: 100%;
    height: 240px;
    padding: 30px;
    margin: 1rem 0;
    border-radius: 20px;
}
.insight_card_btn {
    position: absolute;
    right: 5%;
    top: 7%;
}
.insight_card_detail h4 {
    font-family: BemboStd;
    font-weight: normal;
    font-size: 26px;
    color: #fff;
    margin: 10px;
    text-transform: capitalize;
}
.insight_card_detail {
    position: absolute;
    bottom: 3%;
    left: 3%;
}
.insight_card_btn a {
    font-size: 16px;
    color: #fff;
    border: 1px solid #fff;
    padding: 7px 17px;
    border-radius: 20px;
    text-transform: uppercase;
    font-weight: 600;
    transition: .4s all;
}
.insight_card_btn a:hover{
    color: #2D3956;
    background: #fff;
}
.our_lifeblood {
    padding: 0 0 5rem 0;
}
.mobile-banner{
    display: none;
}
.about_us {
    margin-top: 7rem;
}
.about_us h4 {
    font-size: 45px;
    font-weight: 600;
}

.team_full_desc_img {

    box-shadow: rgb(149 157 165 / 20%) 0px 8px 24px;

    text-align: center;

    width: 80%;

    margin: auto;

}

.team_full_desc_img .team_detail_desc {

    padding: 14px 0;

}

.links_other_slide{

    position: relative;

}

.links_other_slide li::before {
    content: "";
    background-image: url(../image/edge-point.png);
    background-size: contain;
    position: absolute;
    top: 3px;
    left: -7%;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    display: inline-block;
}

.links_other_slide li{

    cursor: pointer;

    position: relative;

    font-family: NeoSansStd-Regular;

    font-size: 16px;

    color: #646464;

    line-height: 20px;

    margin-bottom: 14px;

}

.links_other_slide li a {

    color: #c6962e;

}

.expertise_card {

    display: flex;

    align-items: center;

    justify-content: space-between;

    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;

    padding: 20px;

    margin-bottom: 1.5rem;

    height: 150px;

    transition: .5s all;

}

.expertise_card .expertise_icon {

    margin-right: 20px;

    width: 15%;

}

.expertise_detail {

    border-left: 1px solid #64646452;

    padding-left: 20px;

    width: 100%;

}

.expertise_icon img {

    width: 40px;

}

.expertise_detail h4 {

    font-family: NeoSansStd-Bold;

    font-size: 20px;

    letter-spacing: 0.1em;

    line-height: 24px;

    text-transform: uppercase;

    color: #c6962e;

    margin-bottom: 10px;

}

.expertise_card:hover {

    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;

    transform: scale(1.05);

    transition: .5s all;

}

.expertise_head {

    margin-bottom: 2rem;

    text-align: center;

}

.expertise_head h4{

    font-family: BemboStd;

    font-weight: normal;

    font-size: 40px;

    color: #c6962e;

    margin-bottom: 7px;

    text-transform: capitalize;

}
.solution-tabs {
    position: relative;
    padding-bottom: 80px;
}

.solution-tabs ul.nav-tabs {
    border: 0;
    justify-content: center;
    margin-bottom: 40px;
    column-gap: 0;
    flex-wrap: nowrap;
}
.solution-tabs ul.nav-tabs li a {
    border: 0;
    background: #222b40;
    padding: 10px 5px;
    text-align: center;
    width: 90%;
    margin: 0 auto;
    color: #fff;
    border-radius: 5px;
    transition: 0.5s all;
}
.solution-tabs ul.nav-tabs li a:hover, .solution-tabs ul.nav-tabs li a.active{
    color: #fff;
    background: #c6962e;
    transition: 0.5s all;
}
.solution_thumb{
    position: relative;
}
.solution_thumb img {
    width: 100%;
    height: 350px;
    object-fit: cover;
}
.solution-content {
    box-shadow: rgb(149 157 165 / 29%) 0px 8px 24px;
    padding: 10px;
    width: 90%;
    background: #fff;
    position: relative;
    margin: 0 auto;
    top: -40px;
    text-align: center;
    transition: 0.5s ease;
    padding: 10px 40px 10px 40px;
    overflow: hidden;
}
.solution-content .solution-more-content {
    opacity: 0;
    text-align: left;
    transition: 0.5s ease;
    position: absolute;
    background: #fff;
    z-index: 99;
    width: 100%;
    left: 0;
    padding: 0 40px;
}
.solution-content:hover{
    overflow: visible;
    transition: 0.5s ease;
}
.solution-content:hover .solution-more-content{
    opacity: 1;
    transition: 0.5s ease;
}
.solution-more-content ul li{
    position: relative;
}
.solution-more-content ul li::before {
    content: "";
    background-image: url(../image/edge-point.png);
    position: absolute;
    top: 0;
    left: -7%;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
}
span.wpcf7-spinner {
    position: absolute;
}
.es_newslatter {
    display: flex;
    align-items: center;
}
.es_newslatter .es-field-wrap, .es_newslatter .es-field-wrap label {
    width: 100%;
}
.es_newslatter .es-field-wrap input[type="email"]::focus{
    background: transparent;
}
.es_newslatter .es_submit {
    position: absolute;
    right: 28%;
    top: 15px;
}
.es_newslatter input[type="submit"] {
    border: 0;
    opacity: 0;
    position: absolute;
    z-index: 99999999;
    top: -10px;
    right: 0;
}
input:-internal-autofill-selected {
    background-color: transparent !important;
}
#legalModal {
    backdrop-filter: blur(15px);
}
#legalModal .modal-dialog {
    width: 80%;
    max-width: 80%;
}
#legalModal .modal-dialog p, #legalModal .modal-dialog ol li{
    color: #000;
    font-family: NeoSansStd-Regular;
    font-size:16px;
    letter-spacing: 0.5px;
    font-weight: 400;
    line-height: 1.6;
}
#legalModal .modal-dialog .modal-body {
    height: 610px;
    overflow-y: scroll;
}