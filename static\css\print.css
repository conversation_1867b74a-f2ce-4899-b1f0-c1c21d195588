﻿

/*
Theme Name: Twenty Twenty
Adding print support. The print styles are based on the the great work of
<PERSON> in https://www.jotform.com/blog/css-perfect-print-stylesheet-98272/.
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Margins
# Paddings
# Width
# Typography
# Page breaks
# Links
# Visibility
--------------------------------------------------------------*/
@media print {

  /* Margins */

  @page {
    margin: 2cm;
  }

  .entry-header,
  .site-footer {
    margin: 0;
  }

  /* Paddings */

  .posts {
    padding: 0;
  }
  
  /* Width */

  .entry-content,
  .entry-content p,
  .section-inner,
  .section-inner.max-percentage,
  .section-inner.medium,
  .section-inner.small,
  .section-inner.thin {
    max-width: 100%;
    width: 100%;
  }

  /* Fonts */

  body {
    background: #fff !important;
    color: #000;
    font: 13pt Georgia, "Times New Roman", Times, serif;
    line-height: 1.3;
  }

  h1 {
    font-size: 20pt;
  }

  h2,
  h2.entry-title,
  h3,
  h4,
  .has-normal-font-size,
  .has-regular-font-size,
  .has-large-font-size,
  .comments-header {
    font-size: 14pt;
    margin-top: 1cm;
  }

  /* Page breaks */

  a {
    page-break-inside: avoid;
  }

  blockquote {
    page-break-inside: avoid;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  img {
    page-break-inside: avoid;
    page-break-after: avoid;
  }

  table,
  pre {
    page-break-inside: avoid;
  }

  ul,
  ol,
  dl {
    page-break-before: avoid;
  }

  /* Links */
  
  a:link,
  a:visited,
  a {
    background: transparent;
    font-weight: bold;
    text-decoration: underline;
  }

  a {
    page-break-inside: avoid;
  }

  a[href^="http"]:after {
    content: " < " attr(href) "> ";
  }

  a:after > img {
    content: "";
  }

  article a[href^="#"]:after {
    content: "";
  }

  a:not(:local-link):after {
    content: " < " attr(href) "> ";
  }

  /* Visibility */

  #site-header,
  .comment-form,
  .comments-wrapper,
  .comment .comment-metadata,
  .footer-social-wrapper,
  .footer-widgets-outer-wrapper,
  .header-navigation-wrapper,
  .entry-categories,
  .post-comment-link.meta-wrapper,
  .pagination-wrapper,
  .pagination-single,
  .post-meta-wrapper.post-meta-edit-link-wrapper,
  .post-meta-wrapper.post-meta-single-bottom,
  .post-separator,
  .site-logo img {
    display: none;
  }

  .entry-content .wp-block-button .wp-block-button__link,
  .entry-content .wp-block-button .wp-block-file__button,
  .entry-content .button {
    background: none;
    color: #000;
  }
}
