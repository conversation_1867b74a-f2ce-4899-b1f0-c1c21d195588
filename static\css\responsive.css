﻿/* For Mobile devices and tablet both Media Query:- */


@media only screen and (max-width: 991px) {

    .desktop-banner{
        display: none;
    }

    .mobile-banner{
        display: block;
    }

    .our_edge_content_wrap{

        left: 0;

        margin: 1rem;

    }

    .our_priority_slider_wrap {

        margin-top: 0rem;

        margin-right: 0rem;

        margin-left: 0rem;

    }

    .single_managment_team_wrap .member_img_detail_wrap .member_img_wrap {

        width: 250px;

        height: 250px;

        margin: auto;

    }

    .single_managment_team_wrap .member_img_detail_wrap {     

        margin-bottom: 2rem;

    }

    .about_content_wrap {

        padding: 2rem 0rem;

    }

    .team-member figure {

        width: 100%;

        height: 340px;

    }

    .realtion_team_member figure {

        width: 230px;

        height: 230px;

    }

    .get_touch_wrap {  

        width: 90%;

    }

    .right_content {

        right: 0;

    }

    .left_content {

        left: 0;

    }

    .fund_and_exter_manage_content {

        margin: 1rem 0;

    }   

    .fund_and_exter_manag_img {

        height: auto;

    }

    .our_services_inner_content_wrap .fund_extren_managment_content_img:nth-child(odd) .row {

        flex-direction: column-reverse;

    }

    .fund_and_exter_manage_content ul li::before {      

        left: -13%;

    }  

    .fund_extren_managment_content_img.pad_t{

        padding-top: 0;

    }

    .external_and_fund_manag_section .common_heading {

        margin: 4rem 0;

    }

    

}

/* For Mobile devices Media Query:- */

@media only screen and (max-width: 767px) {

    .modal-dialog.team-modal {
        max-width: 95%;
        margin: 0 auto;
    }

    #legalModal .modal-dialog {
        width: 95%;
        max-width: 95%;
        margin: 10px auto;
    }

    #legalModal .modal-dialog .modal-body {
        height: 445px;
        overflow-y: scroll;
    }

    .header_icons {
        top: 15px;
    }

    .headerbg::before {
        height: 70px;
    }

    .logo-div img {

        width: 110px;

    }

    #nav-icon1 {

        width: 20px;

    }

    small.serach_icon {

        right: 160%;

    }

    .main_banner_section {

        height: 35vh;

    }

    .banner_content_wrap h1 {

        font-size: 40px;

        line-height: 37px;

    } 

    .pad_tb {

        padding: 1rem 0;

    }   

    .home_conatct_form_wrap {

        padding: 0rem;

    }

    .about_content_wrap {

        padding: 1rem;

    }

    .our-value-slider .owl-nav {

        top: -7%;

        right: 0;

    }

    .our_edge_points li p {

        font-size: 14px;

        line-height: 20px;

    }

    .our_edge_content_wrap {

        padding: 20px;

    }

    .our_edge_content_wrap{

        margin: 1rem 0 !important;

    }

    .overlap_first_img {

        top: 0;

        right: 0px;

        width: 100%;

        margin: 1rem 0;

    }

    .overlap_second_img {       

        bottom: 0;

        left: 0;

    }

    .copyright_section {

        padding-top: 1rem;

        text-align: center;

    }

    .footer_social_icon {

        text-align: center;

        margin: 1rem 0;

    }

    .footer-newslatter input{

        width: 100%;

    }

    footer.footer_section {       

        padding-top: 15px;

    }

    .timeline_join_row {

        width: 4em;

    }

    .year_circle {

        font-size: 13px;

        width: 65px;

        height: 65px;

        padding: 3px;

    }

    .content_row_wrap {

        width: 80%;

    }

    .timeline_content {

        width: 68%;

    }

    .inner_banner_section {

        height: 40vh;

        background-size: 120% 40%;

        background-position: top;

    }

    .team_select {

        width: 100%;

        flex-wrap: wrap;

    }

    .team_member_filter{

        margin-bottom: 0;

    }

    .member_search .has-search {

        width: 90%;

        margin: auto;

    }

    .team_select .form-select{

        margin-bottom: 1rem;

    }

    .member_search {

        width: 100%;

    }

    .member_search input{

        margin: auto;

    }

    .contact_accordion .accordion-button, .contact_accordion .accordion-button:not(.collapsed) {

        font-size: 22px;

    }

    .rockstead_team_section {

        padding-top: 3rem;

    }

    .about_left_img::before {

        left: -50px;

    }

    .formSearch .modal-dialog {

        max-width: 94%;

    }

    .formSearch .modal-body {

        padding: 12px;

    }

    .wp-block-search__button.common_button {

        padding: 13px 18px;

        margin-left: 7px;

    }

    .history_timeline .yellow_grid{

        display: none;

    }

    .overlap_first_img{

        text-align: center;

    }

    .solution-tabs ul.nav-tabs li {
        margin-bottom: 20px;
        width: 65%;
    }

    .expertise_card {
        display: block;
        height: auto;
        text-align: center;
    }
    .expertise_card .expertise_icon {
        margin: 0 auto 15px auto !important;
    }
    .expertise_detail{
        border: 0;
    }

    .team_full_desc_section .content_center {
        text-align: center;
        padding: 40px 0 0 0;
    }
   
    .single_managment_team_wrap ul {
        padding: 0;
    }

    .single_managment_team_wrap ul li {
        font-size: 15px;
        position: relative;
        margin-bottom: 8px;
        text-align: center;
    }

    .single_managment_team_wrap ul li:after {
        display: none;
    }

}




/* For Ipad devices 768x1024 Media Query:- */

@media only screen and (min-width: 768px) and (max-width: 991px) {

    .main_banner_section {

        height: 40vh;

    }

    .banner_content_wrap h1 {

        font-size: 45px;

        line-height: 50px;

    }

    .our-value-slider .owl-nav {

        position: absolute;

        top: -5%;

        right: 2%;

    }

    .timeline_content {

        width: 70%;

    }

    .timeline_join_row {

        width: 4em;

    }

    .inner_banner_section {

        height: 40vh;

        background-size: 100% 40%;

        background-position: top;

    }

    .team_select {

        width: 85%;

    }

    .member_search {

        width: 100%;

    }

    .get_touch_wrap p {

        font-size: 12px;

    }

    .solution-tabs ul.nav-tabs li {
        margin-bottom: 20px;
        width: 26%;
    }

    .expertise_card {
        display: block;
        height: 240px;
        text-align: center;
    }
    .expertise_card .expertise_icon {
        margin: 0 auto 15px auto !important;
    }
    .expertise_detail{
        border: 0;
    }

    .team_full_desc_section .content_center {
        padding: 40px 0 0 0;
    }

}



/* For Ipad-Pro devices 1024x1366 Media Query:- */

@media only screen and (min-width: 992px) and (max-width: 1024px) {

    .main_banner_section {

        height: 40vh;

    }

    .our_edge_content_wrap {

        padding: 1rem 2rem;

    }

    .overlap_first_img {

        right: 0;

    }

    .overlap_second_img {

        left: -20px;

    }

    .inner_banner_section {

        height: 32vh;

        background-size: 100% 40%;

        background-position: top;

    }

    .team_select {

        width: 75%;

    }

}

@media only screen and (min-width: 1028px) and (max-width: 1200px) {

    .solution-tabs ul.nav-tabs li {
        margin-bottom: 20px;
        width: 40%;
    }

}



/* For Small Desktop Screen Resolution 1366x768 Media:- */

@media only screen and (max-width: 1366px) {}



/* For MacBook Pro Laptop 1440x990 Media Query:- */

@media only screen and (min-width: 1367px) and (max-width: 1649px) {}



/* Screen Resolution Big screen Laptop 1920x1080 Media:- */

@media only screen and (min-width: 1920px) {}


@media only screen and (max-width: 568px) {

    .aboutus_section, .our_journey_section, .home_contact_section, .about_content_wrap {
        text-align: center;
    }
    .accordion-button{
        justify-content: center;
    }
    .team_full_desc_section .content_center {
        text-align: center;
        padding: 40px 0 0 0;
    }
    .expertise_card {
        display: block;
        height: auto;
        text-align: center;
    }
    .expertise_card .expertise_icon {
        margin: 0 auto 15px auto !important;
    }
    .expertise_detail{
        border: 0;
    }

}