﻿AOS.init({
    duration: 1000
});


//header on scroll js

$(window).scroll(function () {
    var scroll = $(window).scrollTop();
    if (scroll >= 100) {
        $(".main-header").addClass("headerbg");
    } else {
        $(".main-header").removeClass("headerbg");
    }
});
// Header js

$(function() {
    var box = $('.menubarbox');
    var button = $('.open-menu, .header-menu');
    button.on('click', function(){
        box.toggleClass('menubarbox-bottombar');
    });
});

$(document).ready(function(){
    $(".hamburger").click(function(){
        $(this).toggleClass("is-active");
    });
	
/**
 * Trigger a callback when the selected images are loaded:
 * @param {String} selector
 * @param {Function} callback
  */
var onImgLoad = function(selector, callback){
    $(selector).each(function(){
        if (this.complete || /*for IE 10-*/ $(this).height() > 0) {
            callback.apply(this);
        }
        else {
            $(this).on('load', function(){
                callback.apply(this);
            });
        }
    });
};

onImgLoad(".about_left_img img", function(){
    console.log("image is now loaded");
    jQuery(".about_left_img img").attr("alt","Asset Management Firm");
});

onImgLoad(".our_journey_section .img-box img", function(){
    console.log("image is now 1 loaded");
    jQuery(".our_journey_section .img-box img").attr("alt","Top Asset Management Firm");
});
	
onImgLoad(".home_contact_section .overlap_single_img.overlap_first_img img", function(){
    console.log("image is now 2 loaded");
    jQuery(".home_contact_section .overlap_single_img.overlap_first_img img").attr("alt","Financial Advisory Services");
});	

onImgLoad(".home_contact_section .overlap_single_img.overlap_second_img img", function(){
    console.log("image is now 3 loaded");
    jQuery(".home_contact_section .overlap_single_img.overlap_second_img img").attr("alt","Financial Advisory Singapore");
});	

	/* External Assets Management Singpage page */
onImgLoad("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-1106", function(){
    //console.log("eam image is now 1 loaded");
    jQuery("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-1106").attr("alt","Asset Management Singapore");
});	

onImgLoad("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-865", function(){
    //console.log("eam image is now 2 loaded");
    jQuery("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-865").attr("alt","Private Wealth Management Singapore");
});	

onImgLoad("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-1105", function(){
    //console.log("eam image is now 3 loaded");
    jQuery("body.page-id-860 .wp-block-column-is-layout-flow img.wp-image-1105").attr("alt","Wealth Asset Management");
});	

 /* Rockstead Investment Page */
onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-885", function(){
    //console.log("rip image is now 1 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-885").attr("alt","Capital Protection - Fund Management Company");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-886", function(){
    //console.log("rip image is now 2 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-886").attr("alt","Fixed Income - Fund Management Company");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-887", function(){
    //console.log("rip image is now 3 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-887").attr("alt","Highly Liquid - Fund Management Company");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-966", function(){
    //console.log("rip image is now 4 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-966").attr("alt","Fund Management Company");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-903", function(){
    //console.log("rip image is now 5 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-903").attr("alt","Fund Management Company");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-917", function(){
    //console.log("rip image is now 6 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-917").attr("alt","Trusted Fund Management Singapore");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-918", function(){
    //console.log("rip image is now 7 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-918").attr("alt","Professional Fund Management Singapore");
});

onImgLoad("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-919", function(){
    //console.log("rip image is now 8 loaded");
    jQuery("body.page-id-879 .wp-block-column-is-layout-flow img.wp-image-919").attr("alt","Registered Fund Management Company");
});

/* Family Office In Singapore */
onImgLoad("body.page-id-219 section.team_full_desc_section.pad_tb .team_full_desc_img img", function(){
    console.log("fos image is now 1 loaded");
    jQuery("body.page-id-219 section.team_full_desc_section.pad_tb .team_full_desc_img img").attr("alt","Setting Up Family Office In Singapore");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_1 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 2 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_1 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Setting Up Family Office In Singapore");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_2 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 3 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_2 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Family Office Singapore");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_3 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 4 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_3 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Family Office in Singapore");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_4 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 5 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_4 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Family Office Services");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_5 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 6 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_5 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Family Office Services Singapore");
});

onImgLoad("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_5 .col-lg-7.col-md-7 img.img-fluid", function(){
    console.log("fos image is now 7 loaded");
    jQuery("body.page-id-219 .solution-tabs .tab-container-one .tab-content div#solution_5 .col-lg-7.col-md-7 img.img-fluid").attr("alt","Family Office Services Singapore");
});

onImgLoad("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:first-child img", function(){
    console.log("fos image is now 8 loaded");
    jQuery("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:first-child img").attr("alt","Single Family Office Singapore");
});

onImgLoad("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(2) img", function(){
    console.log("fos image is now 9 loaded");
    jQuery("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(2) img").attr("alt","Multi Family Office Singapore");
});

onImgLoad("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(3) img", function(){
    console.log("fos image is now 10 loaded");
    jQuery("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(3) img").attr("alt","Professional Family Office Services ");
});

onImgLoad("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(4) img", function(){
    console.log("fos image is now 10 loaded");
    jQuery("body.page-id-219 section#concierge_sloutions .col-lg-6.col-md-6:nth-child(4) img").attr("alt","Family Office Consulting Services");
});
});


