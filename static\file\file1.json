{"name": "Rockstead", "description": "", "url": "https://rockstead.com", "home": "https://rockstead.com/zh", "gmt_offset": "8", "timezone_string": "", "page_for_posts": 0, "page_on_front": 2855, "show_on_front": "page", "namespaces": ["oembed/1.0", "code-snippets/v1", "contact-form-7/v1", "bsf-custom-fonts/v1", "custom-fonts/v1", "email-subscribers/v1", "icegram-express/v1", "post-smtp/v1", "post-smtp/v2", "psd/v1", "redirection/v1", "simple-history/v1", "yoast/v1", "image-optimizer/v1", "elementor/v1", "google-site-kit/v1", "elementor/v1/documents", "elementor-ai/v1", "elementor-pro/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": {"application-passwords": {"endpoints": {"authorization": "https://rockstead.com/wp-admin/authorize-application.php"}}}, "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "需要获取 oEmbed 数据的链接。", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "需要获取 oEmbed 数据的链接。", "type": "string", "format": "uri", "required": true}, "format": {"description": "使用的 oEmbed 格式。", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "嵌入的元素的最大宽度（像素）。", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "嵌入的元素的最大高度（像素）。", "type": "integer", "required": false}, "discover": {"description": "对未经批准的提供者是否进行 oEmbed 发现请求。", "type": "boolean", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/oembed/1.0/proxy"}]}}, "/code-snippets/v1": {"namespace": "code-snippets/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "code-snippets/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/code-snippets/v1"}]}}, "/code-snippets/v1/snippets": {"namespace": "code-snippets/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"name": {"description": "Descriptive title for the snippet.", "type": "string", "required": false}, "desc": {"description": "Descriptive text associated with snippet.", "type": "string", "required": false}, "code": {"description": "Executable snippet code.", "type": "string", "required": false}, "tags": {"description": "List of tag categories the snippet belongs to.", "type": "array", "required": false}, "scope": {"description": "Context in which the snippet is executable.", "type": "string", "required": false}, "active": {"description": "Snippet activation status.", "type": "boolean", "required": false}, "priority": {"description": "Relative priority in which the snippet is executed.", "type": "integer", "required": false}, "network": {"description": "Whether the snippet is network-wide instead of site-wide.", "type": "boolean", "required": false}, "shared_network": {"description": "If a network snippet, whether can be activated on discrete sites instead of network-wide.", "type": "boolean", "required": false}, "modified": {"description": "Date and time when the snippet was last modified, in ISO format.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/code-snippets/v1/snippets"}]}}, "/code-snippets/v1/snippets/(?P<id>[\\d]+)": {"namespace": "code-snippets/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"name": {"description": "Descriptive title for the snippet.", "type": "string", "required": false}, "desc": {"description": "Descriptive text associated with snippet.", "type": "string", "required": false}, "code": {"description": "Executable snippet code.", "type": "string", "required": false}, "tags": {"description": "List of tag categories the snippet belongs to.", "type": "array", "required": false}, "scope": {"description": "Context in which the snippet is executable.", "type": "string", "required": false}, "active": {"description": "Snippet activation status.", "type": "boolean", "required": false}, "priority": {"description": "Relative priority in which the snippet is executed.", "type": "integer", "required": false}, "network": {"description": "Whether the snippet is network-wide instead of site-wide.", "type": "boolean", "required": false}, "shared_network": {"description": "If a network snippet, whether can be activated on discrete sites instead of network-wide.", "type": "boolean", "required": false}, "modified": {"description": "Date and time when the snippet was last modified, in ISO format.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/code-snippets/v1/snippets/schema": {"namespace": "code-snippets/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/code-snippets/v1/snippets/schema"}]}}, "/code-snippets/v1/snippets/(?P<id>[\\d]+)/activate": {"namespace": "code-snippets/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/code-snippets/v1/snippets/(?P<id>[\\d]+)/deactivate": {"namespace": "code-snippets/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/code-snippets/v1/snippets/(?P<id>[\\d]+)/export": {"namespace": "code-snippets/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/code-snippets/v1/snippets/(?P<id>[\\d]+)/export-code": {"namespace": "code-snippets/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/contact-form-7/v1": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "contact-form-7/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/contact-form-7/v1"}]}}, "/contact-form-7/v1/contact-forms": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/contact-form-7/v1/contact-forms"}]}}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}, {"methods": ["DELETE"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback": {"namespace": "contact-form-7/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback/schema": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/refill": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/bsf-custom-fonts/v1": {"namespace": "bsf-custom-fonts/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "bsf-custom-fonts/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/bsf-custom-fonts/v1"}]}}, "/bsf-custom-fonts/v1/admin/settings": {"namespace": "bsf-custom-fonts/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/bsf-custom-fonts/v1/admin/settings"}]}}, "/custom-fonts/v1": {"namespace": "custom-fonts/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "custom-fonts/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/custom-fonts/v1"}]}}, "/custom-fonts/v1/get-tracking-status": {"namespace": "custom-fonts/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/custom-fonts/v1/get-tracking-status"}]}}, "/custom-fonts/v1/update-tracking-status": {"namespace": "custom-fonts/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/custom-fonts/v1/update-tracking-status"}]}}, "/email-subscribers/v1": {"namespace": "email-subscribers/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "email-subscribers/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/email-subscribers/v1"}]}}, "/email-subscribers/v1/subscribers": {"namespace": "email-subscribers/v1", "methods": ["POST", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["POST"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/email-subscribers/v1/subscribers"}]}}, "/icegram-express/v1": {"namespace": "icegram-express/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "icegram-express/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/icegram-express/v1"}]}}, "/icegram-express/v1/color-palette": {"namespace": "icegram-express/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/icegram-express/v1/color-palette"}]}}, "/post-smtp/v1": {"namespace": "post-smtp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "post-smtp/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1"}]}}, "/post-smtp/v1/connect-app": {"namespace": "post-smtp/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1/connect-app"}]}}, "/post-smtp/v1/get-logs": {"namespace": "post-smtp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1/get-logs"}]}}, "/post-smtp/v1/disconnect-site": {"namespace": "post-smtp/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1/disconnect-site"}]}}, "/post-smtp/v1/get-log": {"namespace": "post-smtp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1/get-log"}]}}, "/post-smtp/v1/resend-email": {"namespace": "post-smtp/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v1/resend-email"}]}}, "/post-smtp/v2": {"namespace": "post-smtp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "post-smtp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v2"}]}}, "/post-smtp/v2/get-logs": {"namespace": "post-smtp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v2/get-logs"}]}}, "/post-smtp/v2/validate-license": {"namespace": "post-smtp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/post-smtp/v2/validate-license"}]}}, "/psd/v1": {"namespace": "psd/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "psd/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1"}]}}, "/psd/v1/get-logs": {"namespace": "psd/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/get-logs"}]}}, "/psd/v1/get-details": {"namespace": "psd/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/get-details"}]}}, "/psd/v1/resend-email": {"namespace": "psd/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/resend-email"}]}}, "/psd/v1/email-count": {"namespace": "psd/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/email-count"}]}}, "/psd/v1/minimize-maximize-ad": {"namespace": "psd/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/minimize-maximize-ad"}]}}, "/psd/v1/get-failed-logs": {"namespace": "psd/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/get-failed-logs"}]}}, "/psd/v1/open-notification": {"namespace": "psd/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/open-notification"}]}}, "/psd/v1/remove-notification": {"namespace": "psd/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/psd/v1/remove-notification"}]}}, "/redirection/v1": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "redirection/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1"}]}}, "/redirection/v1/redirect": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/redirect"}]}}, "/redirection/v1/redirect/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/redirection/v1/redirect/post": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"text": {"description": "Text to match", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/redirect/post"}]}}, "/redirection/v1/bulk/redirect/(?P<bulk>delete|enable|disable|reset)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "global": {"description": "Apply bulk action globally, as per filters", "type": "boolean", "required": false}, "items": {"description": "Array of IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/group": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/group"}]}}, "/redirection/v1/group/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}}}]}, "/redirection/v1/bulk/group/(?P<bulk>delete|enable|disable)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/log": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/log"}]}}, "/redirection/v1/bulk/log/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/404": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/404"}]}}, "/redirection/v1/bulk/404/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}}}]}, "/redirection/v1/setting": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/setting"}]}}, "/redirection/v1/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"name": {"description": "Name", "type": "string", "required": false}, "value": {"description": "Value", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/plugin"}]}}, "/redirection/v1/plugin/delete": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/plugin/delete"}]}}, "/redirection/v1/plugin/test": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/plugin/test"}]}}, "/redirection/v1/plugin/data": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"upgrade": {"description": "Upgrade parameter", "type": "string", "enum": ["stop", "skip", "retry"], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/plugin/data"}]}}, "/redirection/v1/import/file/(?P<group_id>\\d+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/redirection/v1/import/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/redirection/v1/import/plugin"}]}}, "/redirection/v1/export/(?P<module>1|2|3|all)/(?P<format>csv|apache|nginx|json)": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/simple-history/v1": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "simple-history/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1"}]}}, "/simple-history/v1/events": {"namespace": "simple-history/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "type": {"description": "Type of result to return.", "type": "string", "default": "overview", "enum": ["overview", "occasions"], "required": false}, "logRowID": {"description": "Limit result set to rows with id lower than this.", "type": "integer", "required": false}, "occasionsID": {"description": "Limit result set to rows with occasionsID equal to this.", "type": "string", "required": false}, "occasionsCount": {"description": "The number of occasions to get.", "type": "integer", "required": false}, "occasionsCountMaxReturn": {"description": "The max number of occasions to return.", "type": "integer", "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": null, "required": false}, "max_id_first_page": {"description": "Limit result set to rows with id equal or lower than this.", "type": "integer", "required": false}, "since_id": {"description": "Limit result set to rows with id greater than this, i.e. more recent than since_id.", "type": "integer", "required": false}, "date_from": {"description": "Limit result set to rows with date greater than or equal to this unix timestamp.", "type": "string", "required": false}, "date_to": {"description": "Limit result set to rows with date less than or equal to this unix timestamp.", "type": "string", "required": false}, "dates": {"description": "Limit result set to rows with date within this range.", "type": "array", "items": {"type": "string"}, "required": false}, "lastdays": {"description": "Limit result set to rows with date within this range.", "type": "integer", "required": false}, "months": {"description": "Limit result set to rows with date within this range. Format: Y-m.", "type": "array", "items": {"type": "string"}, "required": false}, "loglevels": {"description": "Limit result set to rows with log levels.", "type": "array", "items": {"type": "string"}, "required": false}, "loggers": {"description": "Limit result set to rows with loggers.", "type": "array", "items": {"type": "string"}, "required": false}, "messages": {"description": "Limit result set to rows with messages. Format: LoggerSlug:message.", "type": "array", "items": {"type": "string"}, "required": false}, "users": {"description": "Limit result set to rows with user ids.", "type": "array", "items": {"type": "integer"}, "required": false}, "user": {"description": "Limit result set to rows with user id.", "type": "integer", "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "include_sticky": {"description": "Include sticky events in the result set.", "type": "boolean", "default": false, "required": false}, "only_sticky": {"description": "Only return sticky events.", "type": "boolean", "default": false, "required": false}}}, {"methods": ["POST"], "args": {"message": {"type": "string", "description": "Short message to log", "required": true}, "note": {"type": "string", "description": "Additional note or details about the event", "required": false}, "level": {"type": "string", "enum": ["emergency", "alert", "critical", "error", "warning", "notice", "info", "debug"], "default": "info", "description": "Log level", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/events"}]}}, "/simple-history/v1/events/has-updates": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "type": {"description": "Type of result to return.", "type": "string", "default": "overview", "enum": ["overview", "occasions"], "required": false}, "logRowID": {"description": "Limit result set to rows with id lower than this.", "type": "integer", "required": false}, "occasionsID": {"description": "Limit result set to rows with occasionsID equal to this.", "type": "string", "required": false}, "occasionsCount": {"description": "The number of occasions to get.", "type": "integer", "required": false}, "occasionsCountMaxReturn": {"description": "The max number of occasions to return.", "type": "integer", "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": null, "required": false}, "max_id_first_page": {"description": "Limit result set to rows with id equal or lower than this.", "type": "integer", "required": false}, "since_id": {"description": "Limit result set to rows with id greater than this, i.e. more recent than since_id.", "type": "integer", "required": true}, "date_from": {"description": "Limit result set to rows with date greater than or equal to this unix timestamp.", "type": "string", "required": false}, "date_to": {"description": "Limit result set to rows with date less than or equal to this unix timestamp.", "type": "string", "required": false}, "dates": {"description": "Limit result set to rows with date within this range.", "type": "array", "items": {"type": "string"}, "required": false}, "lastdays": {"description": "Limit result set to rows with date within this range.", "type": "integer", "required": false}, "months": {"description": "Limit result set to rows with date within this range. Format: Y-m.", "type": "array", "items": {"type": "string"}, "required": false}, "loglevels": {"description": "Limit result set to rows with log levels.", "type": "array", "items": {"type": "string"}, "required": false}, "loggers": {"description": "Limit result set to rows with loggers.", "type": "array", "items": {"type": "string"}, "required": false}, "messages": {"description": "Limit result set to rows with messages. Format: LoggerSlug:message.", "type": "array", "items": {"type": "string"}, "required": false}, "users": {"description": "Limit result set to rows with user ids.", "type": "array", "items": {"type": "integer"}, "required": false}, "user": {"description": "Limit result set to rows with user id.", "type": "integer", "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "include_sticky": {"description": "Include sticky events in the result set.", "type": "boolean", "default": false, "required": false}, "only_sticky": {"description": "Only return sticky events.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/events/has-updates"}]}}, "/simple-history/v1/events/(?P<id>[\\d]+)": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the event.", "type": "integer", "required": false}}}]}, "/simple-history/v1/events/(?P<id>[\\d]+)/stick": {"namespace": "simple-history/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the event.", "type": "integer", "required": false}}}]}, "/simple-history/v1/events/(?P<id>[\\d]+)/unstick": {"namespace": "simple-history/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the event.", "type": "integer", "required": false}}}]}, "/simple-history/v1/search-options": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/search-options"}]}}, "/simple-history/v1/search-user": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/search-user"}]}}, "/simple-history/v1/stats/summary": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/summary"}]}}, "/simple-history/v1/stats/users": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/users"}]}}, "/simple-history/v1/stats/content": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/content"}]}}, "/simple-history/v1/stats/media": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/media"}]}}, "/simple-history/v1/stats/plugins": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/plugins"}]}}, "/simple-history/v1/stats/core": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/core"}]}}, "/simple-history/v1/stats/peak-days": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/peak-days"}]}}, "/simple-history/v1/stats/peak-times": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/peak-times"}]}}, "/simple-history/v1/stats/activity-overview": {"namespace": "simple-history/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "date_from": {"description": "Start date as Unix timestamp. If not provided, defaults to 28 days ago.", "type": "integer", "required": false}, "date_to": {"description": "End date as Unix timestamp. If not provided, defaults to end of today.", "type": "integer", "required": false}, "limit": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "include_details": {"description": "Whether to include detailed stats.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/simple-history/v1/stats/activity-overview"}]}}, "/yoast/v1": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "yoast/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1"}]}}, "/yoast/v1/file_size": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"type": "string", "description": "The url to retrieve", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/file_size"}]}}, "/yoast/v1/statistics": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/statistics"}]}}, "/yoast/v1/new-content-type-visibility/dismiss-post-type": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"postTypeName": {"required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/new-content-type-visibility/dismiss-post-type"}]}}, "/yoast/v1/new-content-type-visibility/dismiss-taxonomy": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"taxonomyName": {"required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/new-content-type-visibility/dismiss-taxonomy"}]}}, "/yoast/v1/readability_scores": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"contentType": {"type": "string", "required": true}, "taxonomy": {"type": "string", "default": "", "required": false}, "term": {"type": "integer", "default": null, "required": false}, "troubleshooting": {"type": "bool", "default": null, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/readability_scores"}]}}, "/yoast/v1/seo_scores": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"contentType": {"type": "string", "required": true}, "taxonomy": {"type": "string", "default": "", "required": false}, "term": {"type": "integer", "default": null, "required": false}, "troubleshooting": {"type": "bool", "default": null, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/seo_scores"}]}}, "/yoast/v1/introductions/(?P<introduction_id>[\\w-]+)/seen": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"introduction_id": {"type": "string", "required": true}, "is_seen": {"type": "bool", "default": true, "required": false}}}]}, "/yoast/v1/wistia_embed_permission": {"namespace": "yoast/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"value": {"type": "bool", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wistia_embed_permission"}]}}, "/yoast/v1/alerts/dismiss": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"key": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/alerts/dismiss"}]}}, "/yoast/v1/configuration/site_representation": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"company_or_person": {"type": "string", "enum": ["company", "person"], "required": true}, "company_name": {"type": "string", "required": false}, "company_logo": {"type": "string", "required": false}, "company_logo_id": {"type": "integer", "required": false}, "person_logo": {"type": "string", "required": false}, "person_logo_id": {"type": "integer", "required": false}, "company_or_person_user_id": {"type": "integer", "required": false}, "description": {"type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/site_representation"}]}}, "/yoast/v1/configuration/social_profiles": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"facebook_site": {"type": "string", "required": false}, "twitter_site": {"type": "string", "required": false}, "other_social_urls": {"type": "array", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/social_profiles"}]}}, "/yoast/v1/configuration/check_capability": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"user_id": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/check_capability"}]}}, "/yoast/v1/configuration/enable_tracking": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"tracking": {"type": "boolean", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/enable_tracking"}]}}, "/yoast/v1/configuration/save_configuration_state": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"finishedSteps": {"type": "array", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/save_configuration_state"}]}}, "/yoast/v1/configuration/get_configuration_state": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/configuration/get_configuration_state"}]}}, "/yoast/v1/import/(?P<plugin>[\\w-]+)/(?P<type>[\\w-]+)": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/yoast/v1/get_head": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/get_head"}]}}, "/yoast/v1/indexing/posts": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/posts"}]}}, "/yoast/v1/indexing/terms": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/terms"}]}}, "/yoast/v1/indexing/post-type-archives": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/post-type-archives"}]}}, "/yoast/v1/indexing/general": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/general"}]}}, "/yoast/v1/indexing/prepare": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/prepare"}]}}, "/yoast/v1/indexing/indexables-complete": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/indexables-complete"}]}}, "/yoast/v1/indexing/complete": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/indexing/complete"}]}}, "/yoast/v1/link-indexing/posts": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/link-indexing/posts"}]}}, "/yoast/v1/link-indexing/terms": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/link-indexing/terms"}]}}, "/yoast/v1/integrations/set_active": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"active": {"type": "boolean", "required": true}, "integration": {"type": "string", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/integrations/set_active"}]}}, "/yoast/v1/meta/search": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/meta/search"}]}}, "/yoast/v1/semrush/authenticate": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"code": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/semrush/authenticate"}]}}, "/yoast/v1/semrush/country_code": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"country_code": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/semrush/country_code"}]}}, "/yoast/v1/semrush/related_keyphrases": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"keyphrase": {"required": true}, "country_code": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/semrush/related_keyphrases"}]}}, "/yoast/v1/wincher/authorization-url": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/authorization-url"}]}}, "/yoast/v1/wincher/authenticate": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"code": {"required": true}, "websiteId": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/authenticate"}]}}, "/yoast/v1/wincher/keyphrases/track": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"keyphrases": {"required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/keyphrases/track"}]}}, "/yoast/v1/wincher/keyphrases": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"keyphrases": {"required": false}, "permalink": {"required": false}, "startAt": {"required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/keyphrases"}]}}, "/yoast/v1/wincher/keyphrases/untrack": {"namespace": "yoast/v1", "methods": ["DELETE"], "endpoints": [{"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/keyphrases/untrack"}]}}, "/yoast/v1/wincher/account/limit": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/account/limit"}]}}, "/yoast/v1/wincher/account/upgrade-campaign": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/wincher/account/upgrade-campaign"}]}}, "/yoast/v1/workouts": {"namespace": "yoast/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/yoast/v1/workouts"}]}}, "/image-optimizer/v1": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "image-optimizer/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1"}]}}, "/image-optimizer/v1/connect/authorize": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/authorize"}]}}, "/image-optimizer/v1/connect/disconnect": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/disconnect"}]}}, "/image-optimizer/v1/connect/deactivate": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/deactivate"}]}}, "/image-optimizer/v1/connect/deactivate_and_disconnect": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/deactivate_and_disconnect"}]}}, "/image-optimizer/v1/connect/version": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/version"}]}}, "/image-optimizer/v1/connect/switch_domain": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/connect/switch_domain"}]}}, "/image-optimizer/v1/stats": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/stats"}]}}, "/image-optimizer/v1/stats/optimization-details": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/stats/optimization-details"}]}}, "/image-optimizer/v1/optimize/image": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/optimize/image"}]}}, "/image-optimizer/v1/optimize/bulk": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/optimize/bulk"}]}}, "/image-optimizer/v1/optimize/bulk/images": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/optimize/bulk/images"}]}}, "/image-optimizer/v1/optimize/status": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/optimize/status"}]}}, "/image-optimizer/v1/optimize/bulk/cancel": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/optimize/bulk/cancel"}]}}, "/image-optimizer/v1/backups": {"namespace": "image-optimizer/v1", "methods": ["DELETE"], "endpoints": [{"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/backups"}]}}, "/image-optimizer/v1/backups/restore/(?P<image_id>\\d+)": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/image-optimizer/v1/backups/restore": {"namespace": "image-optimizer/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/backups/restore"}]}}, "/image-optimizer/v1/whats-new": {"namespace": "image-optimizer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/image-optimizer/v1/whats-new"}]}}, "/elementor/v1": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1"}]}}, "/elementor/v1/site-editor": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-editor"}]}}, "/elementor/v1/site-editor/templates": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-editor/templates"}]}}, "/elementor/v1/site-editor/templates/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}]}, "/elementor/v1/site-editor/conditions-config": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-editor/conditions-config"}]}}, "/elementor/v1/site-editor/templates-conditions/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}]}, "/elementor/v1/site-editor/templates-conditions-conflicts": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-editor/templates-conditions-conflicts"}]}}, "/google-site-kit/v1": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "google-site-kit/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1"}]}}, "/google-site-kit/v1/core/site/data/setup-tag": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/setup-tag"}]}}, "/google-site-kit/v1/core/site/data/connection": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/connection"}]}}, "/google-site-kit/v1/core/user/data/authentication": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/authentication"}]}}, "/google-site-kit/v1/core/user/data/disconnect": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/disconnect"}]}}, "/google-site-kit/v1/core/user/data/get-token": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/get-token"}]}}, "/google-site-kit/v1/core/user/data/user-input-settings": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "questions": {"purpose": {"type": "array", "items": {"type": "string"}}, "postFrequency": {"type": "array", "items": {"type": "string"}}, "goals": {"type": "array", "items": {"type": "string"}}, "includeConversionEvents": {"type": "array", "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/user-input-settings"}]}}, "/google-site-kit/v1/core/user/data/audience-settings": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "minProperties": 1, "additionalProperties": false, "properties": {"configuredAudiences": {"type": "array", "items": {"type": "string"}}, "isAudienceSegmentationWidgetHidden": {"type": "boolean"}, "didSetAudiences": {"type": "boolean"}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/audience-settings"}]}}, "/google-site-kit/v1/core/user/data/conversion-reporting-settings": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "minProperties": 1, "additionalProperties": false, "properties": {"newEventsCalloutDismissedAt": {"type": "integer"}, "lostEventsCalloutDismissedAt": {"type": "integer"}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/conversion-reporting-settings"}]}}, "/google-site-kit/v1/core/modules/data/list": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/list"}]}}, "/google-site-kit/v1/core/modules/data/activation": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/activation"}]}}, "/google-site-kit/v1/core/modules/data/info": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/info"}]}}, "/google-site-kit/v1/core/modules/data/check-access": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/check-access"}]}}, "/google-site-kit/v1/modules/(?P<slug>[a-z0-9\\-]+)/data/notifications": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}}}]}, "/google-site-kit/v1/modules/(?P<slug>[a-z0-9\\-]+)/data/settings": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}, "data": {"type": "object", "description": "设置成功", "default": null, "required": false}}}]}, "/google-site-kit/v1/modules/(?P<slug>[a-z0-9\\-]+)/data/data-available": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}}}]}, "/google-site-kit/v1/modules/(?P<slug>[a-z0-9\\-]+)/data/(?P<datapoint>[a-z\\-]+)": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}, "datapoint": {"type": "string", "description": "模块化数据点的地址", "default": null, "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"slug": {"type": "string", "description": "用于模块化标识符", "default": null, "required": false}, "datapoint": {"type": "string", "description": "模块化数据点的地址", "default": null, "required": false}, "data": {"type": "object", "description": "数据用于设置", "default": null, "required": false}}}]}, "/google-site-kit/v1/core/modules/data/recover-modules": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/recover-modules"}]}}, "/google-site-kit/v1/core/modules/data/sharing-settings": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/modules/data/sharing-settings"}]}}, "/google-site-kit/v1/core/user/data/dismissed-items": {"namespace": "google-site-kit/v1", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["DELETE"], "args": {"data": {"type": "object", "description": "", "default": null, "minProperties": 1, "additionalProperties": false, "properties": {"slugs": {"type": "array", "required": true, "items": {"type": "string"}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismissed-items"}]}}, "/google-site-kit/v1/core/user/data/dismiss-item": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismiss-item"}]}}, "/google-site-kit/v1/core/user/data/expirable-items": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/expirable-items"}]}}, "/google-site-kit/v1/core/user/data/set-expirable-item-timers": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "array", "description": "", "default": null, "items": {"type": "object", "additionalProperties": false, "properties": {"slug": {"type": "string", "required": true}, "expiration": {"type": "integer", "required": true}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/set-expirable-item-timers"}]}}, "/google-site-kit/v1/core/user/data/permissions": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/permissions"}]}}, "/google-site-kit/v1/core/user/data/nonces": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/nonces"}]}}, "/google-site-kit/v1/core/user/data/survey-trigger": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"triggerID": {"type": "string", "required": true}, "ttl": {"type": "integer", "minimum": 0}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/survey-trigger"}]}}, "/google-site-kit/v1/core/user/data/survey-event": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"session": {"type": "object", "required": true, "properties": {"session_id": {"type": "string", "required": true}, "session_token": {"type": "string", "required": true}}}, "event": {"type": "object", "required": true, "properties": {"survey_shown": {"type": "object"}, "survey_closed": {"type": "object"}, "question_answered": {"type": "object"}, "completion_shown": {"type": "object"}, "follow_up_link_clicked": {"type": "object"}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/survey-event"}]}}, "/google-site-kit/v1/core/user/data/survey-timeouts": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/survey-timeouts"}]}}, "/google-site-kit/v1/core/user/data/survey": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/survey"}]}}, "/google-site-kit/v1/core/site/data/reset": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/reset"}]}}, "/google-site-kit/v1/core/site/data/reset-persistent": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/reset-persistent"}]}}, "/google-site-kit/v1/core/site/data/developer-plugin": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/developer-plugin"}]}}, "/google-site-kit/v1/core/user/data/tracking": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"enabled": {"type": "boolean", "required": true}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/tracking"}]}}, "/google-site-kit/v1/core/search/data/entity-search": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"query": {"type": "string", "description": "要搜索的文本内容", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/search/data/entity-search"}]}}, "/google-site-kit/v1/core/site/data/admin-bar-settings": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"enabled": {"type": "boolean", "required": false}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/admin-bar-settings"}]}}, "/google-site-kit/v1/core/site/data/notifications": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/notifications"}]}}, "/google-site-kit/v1/core/site/data/mark-notification": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/mark-notification"}]}}, "/google-site-kit/v1/core/site/data/site-health-tag-placement-test": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/site-health-tag-placement-test"}]}}, "/google-site-kit/v1/core/site/data/health-checks": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/health-checks"}]}}, "/google-site-kit/v1/core/user/data/dismissed-tours": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismissed-tours"}]}}, "/google-site-kit/v1/core/user/data/dismiss-tour": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismiss-tour"}]}}, "/google-site-kit/v1/core/user/data/key-metrics": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "properties": {"isWidgetHidden": {"type": "boolean", "required": true}, "widgetSlugs": {"type": "array", "required": false, "maxItems": 8, "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/key-metrics"}]}}, "/google-site-kit/v1/core/user/data/dismissed-prompts": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismissed-prompts"}]}}, "/google-site-kit/v1/core/user/data/dismiss-prompt": {"namespace": "google-site-kit/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"data": {"type": "object", "description": "", "default": null, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/user/data/dismiss-prompt"}]}}, "/google-site-kit/v1/core/site/data/consent-mode": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "minProperties": 1, "additionalProperties": false, "properties": {"enabled": {"type": "boolean"}, "regions": {"type": "array", "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/consent-mode"}]}}, "/google-site-kit/v1/core/site/data/consent-api-info": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/consent-api-info"}]}}, "/google-site-kit/v1/core/site/data/consent-api-activate": {"namespace": "google-site-kit/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/consent-api-activate"}]}}, "/google-site-kit/v1/core/site/data/ads-measurement-status": {"namespace": "google-site-kit/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/ads-measurement-status"}]}}, "/google-site-kit/v1/core/site/data/conversion-tracking": {"namespace": "google-site-kit/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"data": {"type": "object", "description": "", "default": null, "properties": {"settings": {"type": "object", "required": true, "properties": {"enabled": {"type": "boolean", "required": true}}}}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/google-site-kit/v1/core/site/data/conversion-tracking"}]}}, "/elementor/v1/documents": {"namespace": "elementor/v1/documents", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor/v1/documents", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/documents"}]}}, "/elementor/v1/documents/(?P<id>\\d+)/media/import": {"namespace": "elementor/v1/documents", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"required": true}}}]}, "/elementor/v1/cache": {"namespace": "elementor/v1", "methods": ["DELETE"], "endpoints": [{"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/cache"}]}}, "/elementor/v1/globals": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/globals"}]}}, "/elementor/v1/globals/colors": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/globals/colors"}]}}, "/elementor/v1/globals/colors/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/globals/typography": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/globals/typography"}]}}, "/elementor/v1/globals/typography/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/settings/(?P<key>[\\w_-]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/elementor/v1/post": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"excluded_post_types": {"description": "Post type to exclude", "type": ["array", "string"], "default": ["e-floating-buttons", "e-landing-page", "elementor_library", "attachment"], "required": false}, "term": {"description": "Posts to search", "type": "string", "default": "", "required": false}, "post_keys_conversion_map": {"description": "Specify keys to extract and convert, i.e. [\"key_1\" => \"new_key_1\"].", "type": ["array", "string"], "default": [], "required": false}, "max_count": {"description": "Max count of returned items", "type": "number", "default": 100, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/post"}]}}, "/elementor-ai/v1": {"namespace": "elementor-ai/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor-ai/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-ai/v1"}]}}, "/elementor-ai/v1/permissions": {"namespace": "elementor-ai/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-ai/v1/permissions"}]}}, "/elementor/v1/favorites": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/favorites"}]}}, "/elementor/v1/favorites/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Type of favorites.", "type": "string", "required": true}, "favorite": {"description": "The favorite slug to create.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Type of favorites.", "type": "string", "required": true}, "favorite": {"description": "The favorite slug to delete.", "type": "string", "required": true}}}]}, "/elementor/v1/kit-elements-defaults": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/kit-elements-defaults"}]}}, "/elementor/v1/kit-elements-defaults/(?P<type>[\\w\\-\\_]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"type": {"type": "string", "description": "The type of the element.", "required": true}, "settings": {"description": "All the default values for the requested type", "type": "object", "required": true}}}, {"methods": ["DELETE"], "args": {"type": {"type": "string", "description": "The type of the element.", "required": true}}}]}, "/elementor/v1/site-navigation/recent-posts": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"posts_per_page": {"description": "Number of posts to return", "type": "integer", "required": true}, "post_type": {"description": "Post types to retrieve", "type": "array", "default": ["page", "post", "elementor_library"], "required": false}, "post__not_in": {"description": "Post id`s to exclude", "type": "array", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-navigation/recent-posts"}]}}, "/elementor/v1/site-navigation/add-new-post": {"namespace": "elementor/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_type": {"description": "Post type to create", "type": "string", "default": "post", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/site-navigation/add-new-post"}]}}, "/elementor/v1/checklist": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/checklist"}]}}, "/elementor/v1/checklist/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/checklist/steps": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/checklist/steps"}]}}, "/elementor/v1/checklist/steps/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/checklist/steps/(?P<id>[\\w\\-\\_]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"type": "string", "description": "The step id.", "required": true}}}]}, "/elementor/v1/checklist/user-progress": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/checklist/user-progress"}]}}, "/elementor/v1/template-library/templates": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"title": {"type": "string", "description": "The title of the document", "required": false}, "type": {"description": "The document type.", "type": "string", "enum": ["page", "section", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "loop-item"], "required": true}, "content": {"description": "Elementor data object", "type": "object", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/template-library/templates"}]}}, "/elementor/v1/global-widget/templates": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/global-widget/templates"}]}}, "/elementor-pro/v1": {"namespace": "elementor-pro/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor-pro/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-pro/v1"}]}}, "/elementor-pro/v1/posts-widget": {"namespace": "elementor-pro/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-pro/v1/posts-widget"}]}}, "/elementor/v1/form-submissions": {"namespace": "elementor/v1", "methods": ["GET", "DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "order_by": {"description": "Sort collection by object attribute.", "type": "string", "default": "created_at", "enum": ["created_at", "id", "main_meta_id"], "required": false}, "status": {"description": "Limit result set to submissions assigned one or more statuses.", "type": "string", "default": "all", "enum": ["all", "unread", "read", "trash"], "additionalProperties": {"context": "filter"}, "required": false}, "form": {"description": "Limit result set to submissions assigned to specific forms. The form id should follow this pattern {post_id}_{element_id} e.g: 10_476d0ce", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "referer": {"description": "Limit result set to submissions assigned to specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "after": {"description": "Limit response to submissions sent after a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "before": {"description": "Limit response to submissions sent before a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}}}, {"methods": ["DELETE"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}, "force": {"description": "Delete the object permanently.", "type": "boolean", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}, "is_read": {"description": "mark whether the submission was read or not", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/form-submissions"}]}}, "/elementor/v1/form-submissions/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET", "DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}, "force": {"description": "Delete the object permanently.", "type": "boolean", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}, "is_read": {"description": "mark whether the submission was read or not", "type": "boolean", "required": false}, "values": {"description": "Form field values, receive an array, the key should be the form field id and the value should be the value.", "type": "object", "required": false}}}]}, "/elementor/v1/form-submissions/restore/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/form-submissions/restore": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/form-submissions/restore"}]}}, "/elementor/v1/form-submissions/export": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 10000, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "order_by": {"description": "Sort collection by object attribute.", "type": "string", "default": "created_at", "enum": ["created_at", "id", "main_meta_id"], "required": false}, "status": {"description": "Limit result set to submissions assigned one or more statuses.", "type": "string", "default": "all", "enum": ["all", "unread", "read", "trash"], "additionalProperties": {"context": "filter"}, "required": false}, "form": {"description": "Limit result set to submissions assigned to specific forms. The form id should follow this pattern {post_id}_{element_id} e.g: 10_476d0ce", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "referer": {"description": "Limit result set to submissions assigned to specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "after": {"description": "Limit response to submissions sent after a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "before": {"description": "Limit response to submissions sent before a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "additionalProperties": {"context": "filter"}, "required": false}, "format": {"description": "The format of the export (for now only csv).", "enum": ["csv"], "default": "csv", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/form-submissions/export"}]}}, "/elementor/v1/form-submissions/referer": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made, determines fields present in response. (only \"options\" available for now)", "type": "string", "enum": ["options"], "default": "options", "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "value": {"description": "Limit results specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/form-submissions/referer"}]}}, "/elementor/v1/forms": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made, determines fields present in response. (only \"options\" available for now)", "type": "string", "enum": ["options"], "default": "options", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/forms"}]}}, "/elementor-pro/v1/get-post-type-taxonomies": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_type": {"description": "The post type for which to fetch the list of taxonomies.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-pro/v1/get-post-type-taxonomies"}]}}, "/elementor-pro/v1/refresh-loop": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_id": {"description": "The post ID of the page containing the loop.", "type": "integer", "required": true}, "widget_id": {"description": "The ID of the loop widget.", "type": "string", "required": true}, "widget_filters": {"description": "The filters for the loop widget.", "type": "object", "required": true}, "widget_model": {"description": "The model of the loop widget. In Editor mode only.", "type": "object", "required": false}, "pagination_base_url": {"required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-pro/v1/refresh-loop"}]}}, "/elementor-pro/v1/refresh-search": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_id": {"description": "The post ID of the page containing the loop.", "type": "integer", "required": true}, "widget_id": {"description": "The ID of the loop widget.", "type": "string", "required": true}, "widget_model": {"description": "The model of the loop widget. In Editor mode only.", "type": "object", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor-pro/v1/refresh-search"}]}}, "/elementor/v1/notes": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "status": {"type": "string", "description": "The note status (e.g. \"publish\", \"draft\").", "enum": ["publish", "draft"], "default": "publish", "required": false}, "is_resolved": {"type": "boolean", "description": "Whether the note is resolved or not.", "required": false}, "parent_id": {"type": "integer", "description": "The note's parent id (use 0 for top-level).", "required": false}, "post_id": {"type": "integer", "description": "The ID of the post that the note is attached to.", "required": false}, "only_unread": {"type": "boolean", "description": "Show only unread notes (represents an unread thread if one of its replies is unread).", "required": false}, "only_relevant": {"type": "boolean", "description": "Show only notes that are relevant to the current user.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "last_activity_at", "enum": ["last_activity_at", "created_at"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "desc", "enum": ["asc", "desc"], "required": false}}}, {"methods": ["POST"], "args": {"post_id": {"type": "integer", "description": "The id of the post where the note was created at (can be template, post, page, etc.).", "required": true}, "element_id": {"type": "string", "description": "Each note must be attached to an elementor element.", "required": true}, "content": {"type": "string", "description": "The content of the note.", "required": true}, "position": {"type": "object", "properties": {"x": {"required": true, "type": "number"}, "y": {"required": true, "type": "number"}}, "description": "The position of the note.", "required": true}, "mentioned_usernames": {"type": "array", "description": "List of user names that have been mentioned in the note's content.", "default": [], "items": {"type": "string", "sanitize_callback": {}}, "required": false}, "route_post_id": {"description": "The ID of the post that's associated with the route (doesn't always exist, e.g: home page, archive)", "required": false}, "route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "route_title": {"type": "string", "description": "The title of the route where the note was created at.", "required": false}, "parent_id": {"type": "integer", "description": "If the new note is a reply to another note, the parent_id should be the thread's id.", "default": 0, "required": false}, "is_public": {"type": "boolean", "description": "Should this note be visible for everyone or just for its author.", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/notes"}]}}, "/elementor/v1/notes/read-status": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"ids": {"type": "array", "description": "The id's of the notes.", "items": {"type": "integer"}, "required": true}}}, {"methods": ["DELETE"], "args": {"ids": {"type": "array", "description": "The id's of the notes.", "items": {"type": "integer"}, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/notes/read-status"}]}}, "/elementor/v1/notes/summary": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "status": {"type": "string", "description": "The note status (e.g. \"publish\", \"draft\").", "enum": ["publish", "draft"], "default": "publish", "required": false}, "is_resolved": {"type": "boolean", "description": "Whether the note is resolved or not.", "required": false}, "parent_id": {"type": "integer", "description": "The note's parent id (use 0 for top-level).", "required": false}, "post_id": {"type": "integer", "description": "The ID of the post that the note is attached to.", "required": false}, "only_unread": {"type": "boolean", "description": "Show only unread notes (represents an unread thread if one of its replies is unread).", "required": false}, "only_relevant": {"type": "boolean", "description": "Show only notes that are relevant to the current user.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "last_activity_at", "enum": ["last_activity_at", "created_at"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "desc", "enum": ["asc", "desc"], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/notes/summary"}]}}, "/elementor/v1/notes/users": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"limit": {"type": "integer", "description": "Limit the results.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "display_name", "enum": ["user_nicename", "display_name", "user_registered"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "asc", "enum": ["asc", "desc"], "required": false}, "search": {"type": "string", "description": "Filter users by a search term.", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/notes/users"}]}}, "/elementor/v1/notes/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"type": "integer", "description": "Note ID to find.", "required": true}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"type": "integer", "description": "The id the note.", "required": true}, "content": {"type": "string", "description": "The content of the note.", "required": false}, "mentioned_usernames": {"type": "array", "description": "List of user names that have been mentioned in the note's content.", "items": {"type": "string", "sanitize_callback": {}}, "required": false}, "status": {"type": "string", "description": "Note status can be draft or publish.", "enum": ["publish", "draft"], "required": false}, "is_public": {"type": "boolean", "description": "Should this note be visible for everyone or just for its author.", "required": false}, "is_resolved": {"type": "boolean", "description": "Is this note resolved and should be hidden.", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"type": "integer", "description": "The id of the note.", "required": true}, "force": {"type": "boolean", "description": "Determine if it should be deleted permanently or change the status to trash.", "default": false, "required": false}}}]}, "/elementor/v1/library/connect": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"token": {"type": "string", "description": "Connect CLI token", "required": true}}}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/library/connect"}]}}, "/elementor/v1/send-event": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"event_data": {"description": "All the recorded event data in JSON format", "type": "object", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/elementor/v1/send-event"}]}}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "将结果集限制为在 categories 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "是否在限制结果集的项目中包含子项目。", "type": "boolean", "default": false}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "将结果集限制为在 categories 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "是否在限制结果集的项目中包含子项目。", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "将结果集限制为在 tags 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "将结果集限制为在 tags 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "将结果集限制为置顶项目。", "type": "boolean", "required": false}, "ignore_sticky": {"description": "是否忽略置顶文章。", "type": "boolean", "default": true, "required": false}, "format": {"description": "将结果集限制为分配一个或多个指定格式的项。", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "将结果集限制为有特定 menu_order 的文章。", "type": "integer", "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "将结果集限制为具有特定父 ID 的项目。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "将结果集限制为除特定父 ID 之外的所有项。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "将结果集限制为具有特定父 ID 的项目。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "将结果集限制为除特定父 ID 之外的所有项。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "将结果集限制为某一媒体类型的附件。", "type": "string", "enum": ["image", "video", "text", "application", "audio"], "required": false}, "mime_type": {"default": null, "description": "将结果集限制为某一 MIME 类型的附件。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "alt_text": {"description": "在附件未能显示时显示的替换文本。", "type": "string", "required": false}, "caption": {"description": "附件说明文字。", "type": "object", "properties": {"raw": {"description": "附件的说明文字，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 说明文字，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "附件的描述。", "type": "object", "properties": {"raw": {"description": "附件的描述，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 描述，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "附件所属文章的 ID。", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "alt_text": {"description": "在附件未能显示时显示的替换文本。", "type": "string", "required": false}, "caption": {"description": "附件说明文字。", "type": "object", "properties": {"raw": {"description": "附件的说明文字，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 说明文字，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "附件的描述。", "type": "object", "properties": {"raw": {"description": "附件的描述，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 描述，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "附件所属文章的 ID。", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "附件的唯一标识符。", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "已经编辑的图片文件 的 URL。", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "图片编辑数组。", "type": "array", "minItems": 1, "items": {"description": "图片编辑", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "旋转", "properties": {"type": {"description": "旋转类型。", "type": "string", "enum": ["rotate"]}, "args": {"description": "旋转参数。", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "顺时针旋转的角度。", "type": "number"}}}}}, {"title": "裁剪", "properties": {"type": {"description": "裁剪方式。", "type": "string", "enum": ["crop"]}, "args": {"description": "裁剪参数。", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "从左边开始裁剪的水平位置以占用图片宽度的百分比计算。", "type": "number"}, "top": {"description": "从顶部开始裁剪的垂直位置以占用图片高度的百分比计算。", "type": "number"}, "width": {"description": "裁剪宽度以占用图片宽度的百分比计算。", "type": "number"}, "height": {"description": "裁剪的高度以占用图片高度的百分比计算。", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "顺时针旋转的角度量。已弃用：请改为使用 `modifiers` 参数。", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "从 X 轴开始裁剪的位置以占用图片的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "从 Y 轴开始裁剪的位置以占用图片的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "裁剪宽度以图片所占用的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "裁剪高度以图片所站用的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "将结果集限制为在 menus 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "将结果集限制为在 menus 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "将结果集限制为有特定 menu_order 的文章。", "type": "integer", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"default": 0, "description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"default": 0, "description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "将结果集限制为在 wp_pattern_category 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "将结果集限制为在 wp_pattern_category 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "限制为指定的文章 ID。", "type": "integer", "required": false}, "area": {"description": "限制为指定的模板组件区。", "type": "string", "required": false}, "post_type": {"description": "要获取模板的文章类型。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"default": "", "description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "模板的描述。", "type": "string", "required": false}, "status": {"default": "publish", "description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "获取回退所用的模板的别名：", "type": "string", "required": true}, "is_custom": {"description": "指明模板是自定义的，或是属于模板层级的一部分", "type": "boolean", "required": false}, "template_prefix": {"description": "所创建模板的模板前缀。 这被用于提取主模板类型，例如在「taxonomy-books」中将提取「taxonomy」。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "限制为指定的文章 ID。", "type": "integer", "required": false}, "area": {"description": "限制为指定的模板组件区。", "type": "string", "required": false}, "post_type": {"description": "要获取模板的文章类型。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"default": "", "description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "模板的描述。", "type": "string", "required": false}, "status": {"default": "publish", "description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "获取回退所用的模板的别名：", "type": "string", "required": true}, "is_custom": {"description": "指明模板是自定义的，或是属于模板层级的一部分", "type": "boolean", "required": false}, "template_prefix": {"description": "所创建模板的模板前缀。 这被用于提取主模板类型，例如在「taxonomy-books」中将提取「taxonomy」。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "全局样式修订版父版本的 ID。", "type": "integer", "required": false}, "id": {"description": "全局样式修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "主题标识符", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "主题标识符", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "全局样式。", "type": ["object"], "required": false}, "settings": {"description": "全局设置。", "type": ["object"], "required": false}, "title": {"description": "全局样式变体的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "全局样式变体的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "以字符串形式编码的 theme.json 格式的 font-family 声明。", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "以字符串形式编码的 theme.json 格式的 font-family 声明。", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "在 theme.json 格式中以字符串形式编码的 font-face 声明。", "type": "string", "required": true}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "id": {"description": "字体的唯一标识符。", "type": "integer", "required": true}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "id": {"description": "字体的唯一标识符。", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/elementor_library": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/elementor_library"}]}}, "/wp/v2/elementor_library/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/elementor_library/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/elementor_library/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/press-event": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/press-event"}]}}, "/wp/v2/press-event/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/press-event/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/press-event/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}}}]}, "/wp/v2/press-event/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "kit", "not-supported", "page", "section", "cloud-template-preview", "widget", "popup", "header", "footer", "single", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}]}, "/wp/v2/press-event/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/ig_es_campaign": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/ig_es_campaign"}]}}, "/wp/v2/ig_es_campaign/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}}}]}, "/wp/v2/ig_es_campaign/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "_links_to": {"type": "string", "title": "", "description": "", "default": ""}, "_links_to_target": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}}}]}, "/wp/v2/ig_es_campaign/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "文章类型的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "状态的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "限制结果为关联到特定文章类型的分类法。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "分类法的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "parent": {"description": "将结果集限制为指定给特定父项目的项目。", "type": "integer", "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "parent": {"description": "父术语 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "parent": {"description": "父术语 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "locations": {"description": "分配给菜单的位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "是否自动添加顶级页面到此菜单。", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "locations": {"description": "分配给菜单的位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "是否自动添加顶级页面到此菜单。", "type": "boolean", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}}}]}, "/wp/v2/users": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"default": "asc", "description": "设置排序字段升序或降序。", "enum": ["asc", "desc"], "type": "string", "required": false}, "orderby": {"default": "name", "description": "按用户属性对集合进行排序。", "enum": ["id", "include", "name", "registered_date", "slug", "include_slugs", "email", "url"], "type": "string", "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的用户。", "type": "array", "items": {"type": "string"}, "required": false}, "roles": {"description": "将结果集限制为匹配至少一个角色的用户，接受.csv 格式列表或单个角色。", "type": "array", "items": {"type": "string"}, "required": false}, "capabilities": {"description": "将结果集限制为匹配至少一项提供的特定功能的用户。接受 CSV 列表或单个功能。", "type": "array", "items": {"type": "string"}, "required": false}, "who": {"description": "将结果集限制为用户中的所有作者。", "type": "string", "enum": ["authors"], "required": false}, "has_published_posts": {"description": "将结果限制于已发布文章的用户。", "type": ["boolean", "array"], "items": {"type": "string", "enum": {"post": "post", "page": "page", "attachment": "attachment", "nav_menu_item": "nav_menu_item", "wp_block": "wp_block", "wp_template": "wp_template", "wp_template_part": "wp_template_part", "wp_global_styles": "wp_global_styles", "wp_navigation": "wp_navigation", "wp_font_family": "wp_font_family", "wp_font_face": "wp_font_face", "elementor_library": "elementor_library", "press-event": "press-event", "ig_es_campaign": "ig_es_campaign"}}, "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["email", "name", "id", "username", "slug"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"username": {"description": "用户的登录名。", "type": "string", "required": true}, "name": {"description": "用户的显示名。", "type": "string", "required": false}, "first_name": {"description": "用户的名字。", "type": "string", "required": false}, "last_name": {"description": "用户的姓氏。", "type": "string", "required": false}, "email": {"description": "用户的邮箱地址。", "type": "string", "format": "email", "required": true}, "url": {"description": "用户的 URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "用户的描述。", "type": "string", "required": false}, "locale": {"description": "用户的地区语言。", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "用户的昵称。", "type": "string", "required": false}, "slug": {"description": "用户的英数字标识符。", "type": "string", "required": false}, "roles": {"description": "用户被赋予的角色。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "用户的密码（从不被包含）。", "type": "string", "required": true}, "meta": {"description": "元字段。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "日期和时间的偏好设置已更新。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/users"}]}}, "/wp/v2/users/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "用户的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "用户的唯一标识符。", "type": "integer", "required": false}, "username": {"description": "用户的登录名。", "type": "string", "required": false}, "name": {"description": "用户的显示名。", "type": "string", "required": false}, "first_name": {"description": "用户的名字。", "type": "string", "required": false}, "last_name": {"description": "用户的姓氏。", "type": "string", "required": false}, "email": {"description": "用户的邮箱地址。", "type": "string", "format": "email", "required": false}, "url": {"description": "用户的 URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "用户的描述。", "type": "string", "required": false}, "locale": {"description": "用户的地区语言。", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "用户的昵称。", "type": "string", "required": false}, "slug": {"description": "用户的英数字标识符。", "type": "string", "required": false}, "roles": {"description": "用户被赋予的角色。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "用户的密码（从不被包含）。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "日期和时间的偏好设置已更新。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "用户的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为用户不能被移动到回收站。", "required": false}, "reassign": {"type": "integer", "description": "将被删除用户的文章和链接重新指定到此用户 ID。", "required": true}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "用户的登录名。", "type": "string", "required": false}, "name": {"description": "用户的显示名。", "type": "string", "required": false}, "first_name": {"description": "用户的名字。", "type": "string", "required": false}, "last_name": {"description": "用户的姓氏。", "type": "string", "required": false}, "email": {"description": "用户的邮箱地址。", "type": "string", "format": "email", "required": false}, "url": {"description": "用户的 URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "用户的描述。", "type": "string", "required": false}, "locale": {"description": "用户的地区语言。", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "用户的昵称。", "type": "string", "required": false}, "slug": {"description": "用户的英数字标识符。", "type": "string", "required": false}, "roles": {"description": "用户被赋予的角色。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "用户的密码（从不被包含）。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "日期和时间的偏好设置已更新。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "要求为 true，因为用户不能被移动到回收站。", "required": false}, "reassign": {"type": "integer", "description": "将被删除用户的文章和链接重新指定到此用户 ID。", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "由应用程序提供的用于唯一识别的 UUID。建议使用含有 URL 或 DNS 命名空间的 UUID v5。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "应用程序密码名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "由应用程序提供的用于唯一识别的 UUID。建议使用含有 URL 或 DNS 命名空间的 UUID v5。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "应用程序密码名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的评论。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定用户 ID 的评论，需要授权。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定用户 ID 的评论，需要授权。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "将结果集限制为指定作者的邮箱地址，需要授权。", "format": "email", "type": "string", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的评论。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按评论属性对集合进行排序。", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "将结果集限制为指定父 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "确保结果集排除指定父 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "将结果集限制为关联到指定文章 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "将结果集限制为设置特定状态的评论，需要授权。", "type": "string", "required": false}, "type": {"default": "comment", "description": "将结果集限制为设置特定类型的评论，需要授权。", "type": "string", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "用户对象的 ID，如果作者是用户。", "type": "integer", "required": false}, "author_email": {"description": "评论者的邮箱地址。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "评论者的 IP 地址。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "评论者的显示名。", "type": "string", "required": false}, "author_url": {"description": "评论者的网址。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "评论者的 User-Agent。", "type": "string", "required": false}, "content": {"description": "评论的内容。", "type": "object", "properties": {"raw": {"description": "评论的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "评论的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "评论发表的日期（站点时区）。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "评论发表的 GMT 日期。", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "评论的父级 ID。", "type": "integer", "required": false}, "post": {"default": 0, "description": "关联文章对象的 ID。", "type": "integer", "required": false}, "status": {"description": "评论的状态。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "评论所属文章的密码（如果该文章被密码保护）。", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "author": {"description": "用户对象的 ID，如果作者是用户。", "type": "integer", "required": false}, "author_email": {"description": "评论者的邮箱地址。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "评论者的 IP 地址。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "评论者的显示名。", "type": "string", "required": false}, "author_url": {"description": "评论者的网址。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "评论者的 User-Agent。", "type": "string", "required": false}, "content": {"description": "评论的内容。", "type": "object", "properties": {"raw": {"description": "评论的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "评论的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "评论发表的日期（站点时区）。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "评论发表的 GMT 日期。", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "评论的父级 ID。", "type": "integer", "required": false}, "post": {"description": "关联文章对象的 ID。", "type": "integer", "required": false}, "status": {"description": "评论的状态。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "password": {"description": "评论所属文章的密码（如果该文章被密码保护）。", "type": "string", "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "type": {"default": "post", "description": "限制结果为一种对象类型。", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "限制结果为一种或多种对象子类型。", "type": "array", "items": {"enum": ["post", "page", "elementor_library", "press-event", "ig_es_campaign", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "此区块的唯一注册名。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "此区块的属性。", "type": "object", "default": [], "required": false}, "post_id": {"description": "文章上下文的 ID。", "type": "integer", "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "区块名称", "type": "string", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "标题", "description": "站点标题。", "type": "string", "required": false}, "description": {"title": "副标题", "description": "站点副标题。", "type": "string", "required": false}, "url": {"title": "", "description": "站点 URL。", "type": "string", "format": "uri", "required": false}, "email": {"title": "", "description": "此地址被用作管理用途，如新用户通知。", "type": "string", "format": "email", "required": false}, "timezone": {"title": "", "description": "和您在同一个时区的城市。", "type": "string", "required": false}, "date_format": {"title": "", "description": "对所有日期字符串适用的日期格式。", "type": "string", "required": false}, "time_format": {"title": "", "description": "对所有时间字符串适用的时间格式。", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "一周从周几开始。", "type": "integer", "required": false}, "language": {"title": "", "description": "WordPress 地区语言代码。", "type": "string", "required": false}, "use_smilies": {"title": "", "description": "将表情符号如:-) 和:-P 转换为图片显示。", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "默认文章分类。", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "默认文章形式。", "type": "string", "required": false}, "posts_per_page": {"title": "每页最多文章数", "description": "最多显示的博客页面数量。", "type": "integer", "required": false}, "show_on_front": {"title": "在前面显示", "description": "需要在首页上显示的项目", "type": "string", "required": false}, "page_on_front": {"title": "前一页", "description": "需要在首页上显示的页面 ID", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "需要显示最新文章的页面 ID", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "允许其他博客发送链接通知（pingback 和 trackback）到新文章。", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "允许对新文章发表评论", "description": "允许他人在新文章上发表评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "Logo", "description": "站点 logo", "type": "integer", "required": false}, "site_icon": {"title": "图标", "description": "站点图标。", "type": "integer", "required": false}, "image_optimizer_compression_level": {"title": "", "description": "", "type": "string", "required": false}, "image_optimizer_optimize_on_upload": {"title": "", "description": "", "type": "boolean", "required": false}, "image_optimizer_resize_larger_images": {"title": "", "description": "", "type": "boolean", "required": false}, "image_optimizer_resize_larger_images_size": {"title": "", "description": "", "type": "integer", "required": false}, "image_optimizer_exif_metadata": {"title": "", "description": "", "type": "boolean", "required": false}, "image_optimizer_original_images": {"title": "", "description": "", "type": "boolean", "required": false}, "image_optimizer_convert_to_format": {"title": "", "description": "", "type": "string", "required": false}, "image_optimizer_custom_sizes": {"title": "", "description": "", "type": "string", "required": false}, "image_optimizer_help_videos": {"title": "", "description": "", "type": "object", "additionalProperties": true, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "将结果集限制为指定了一个或多个状态的主题。", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "主题的样式表。这是主题的唯一标识。", "type": "string", "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "status": {"description": "限制结果集为具有给定状态的插件。", "type": "array", "items": {"type": "string", "enum": ["inactive", "active"]}, "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "WordPress.org 插件目录别名。", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "插件启用状态。", "type": "string", "enum": ["inactive", "active"], "default": "inactive", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "插件启用状态。", "type": "string", "enum": ["inactive", "active"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "已注册的边栏 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "嵌套小工具。", "type": "array", "items": {"type": ["object", "string"]}, "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": true}, "instance": {"description": "小工具的当前设置实例。", "type": "object", "required": false}, "form_data": {"description": "序列化小工具表单数据，以编码为设置实例。", "type": "string", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": true}, "instance": {"description": "小工具的当前设置实例。", "type": "object", "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "返回小工具的侧边栏。", "type": "string", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "小工具的唯一标识符。", "type": "string", "required": false}, "id_base": {"description": "小工具的类型。对应于 widget-types 端点的 ID。", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "小工具所属的侧边栏。", "type": "string", "required": true}, "instance": {"description": "小工具的实例设置（如果支持）。", "type": "object", "properties": {"encoded": {"description": "实例设置的 Base64 编码表示。", "type": "string", "context": ["edit"]}, "hash": {"description": "实例设置的加密哈希值。", "type": "string", "context": ["edit"]}, "raw": {"description": "未编码的实例设置（如果支持）。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "来自小工具管理表单的 URL 编码表单数据。用于更新不支持实例的小工具（指写）。", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "小工具的唯一标识符。", "type": "string", "required": false}, "id_base": {"description": "小工具的类型。对应于 widget-types 端点的 ID。", "type": "string", "required": false}, "sidebar": {"description": "小工具所属的侧边栏。", "type": "string", "required": false}, "instance": {"description": "小工具的实例设置（如果支持）。", "type": "object", "properties": {"encoded": {"description": "实例设置的 Base64 编码表示。", "type": "string", "context": ["edit"]}, "hash": {"description": "实例设置的加密哈希值。", "type": "string", "context": ["edit"]}, "raw": {"description": "未编码的实例设置（如果支持）。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "来自小工具管理表单的 URL 编码表单数据。用于更新不支持实例的小工具（指写）。", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "强行移除小工具，货将其移动到未启用的小工具侧边栏。", "type": "boolean", "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "将结果集限制为匹配搜索词的区块。", "type": "string", "minLength": 1, "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "minLength": 1, "required": false}, "category": {"description": "将结果限制为与分类 ID 匹配的结果。", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "将结果限制为与关键字 ID 匹配的结果。", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "将结果限制为与样板（别名）匹配的结果。", "type": "array", "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "要处理的 URL。", "type": "string", "format": "uri", "required": true}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "菜单位置的字母数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}}}], "_links": {"self": [{"href": "https://rockstead.com/zh/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}}, "site_logo": 0, "site_icon": 457, "site_icon_url": "https://rockstead.com/wp-content/uploads/2022/04/cropped-rockstead-color-logo.png", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}], "wp:featuredmedia": [{"embeddable": true, "type": "site_icon", "href": "https://rockstead.com/zh/wp-json/wp/v2/media/457"}], "curies": [{"name": "wp", "href": "https://api.w.org/{rel}", "templated": true}]}}